@extends('layouts.app')

@section('styles')
<link href="{{ asset('css/meu-desejo.css') }}" rel="stylesheet">
@endsection

@section('content')
<div class="container">
    <!-- Cabeçalho -->
    <div class="text-center mb-5">
        <h1 class="display-4 mb-3" style="color: #141414;">
            <i class="fas fa-gift me-3" style="color: #b1904e;"></i>Meu Desejo
        </h1>
        <p class="lead text-muted">Cadastre seus dados e escolha os produtos dos seus sonhos</p>
    </div>

    <!-- Indicador de Progresso -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="progress-container">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="step-indicator active" id="step-indicator-1">
                        <div class="step-circle">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="step-label">Dad<PERSON> Pessoais</div>
                    </div>
                    <div class="progress-line"></div>
                    <div class="step-indicator" id="step-indicator-2">
                        <div class="step-circle">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="step-label">Explorar Produtos</div>
                    </div>
                    <div class="progress-line"></div>
                    <div class="step-indicator" id="step-indicator-3">
                        <div class="step-circle">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="step-label">Meus Desejos</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Passo 1: Dados do Funcionário -->
    <div id="step1">
        <form id="etapa1Form">
            <input type="hidden" id="employee_id" name="employee_id">

            <!-- Card de Identificação -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Dados Pessoais</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cpf" class="form-label">CPF <span class="text-danger">*</span></label>
                            <input type="text" id="cpf" class="form-control cpf-mask" placeholder="000.000.000-00">
                            <div id="errorCpf" class="text-danger mt-1"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Nome Completo</label>
                            <input type="text" id="name" class="form-control" disabled>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Telefone</label>
                            <input type="text" id="phone" class="form-control phone-mask" placeholder="(00) 00000-0000">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">E-mail</label>
                            <input type="email" id="email" class="form-control" placeholder="<EMAIL>">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_of_admission" class="form-label">Data de Admissão</label>
                            <input type="date" id="date_of_admission" class="form-control">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="birthday_date" class="form-label">Data de Nascimento</label>
                            <input type="date" id="birthday_date" class="form-control">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card de Empresa -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>Dados Profissionais</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_id" class="form-label">Empresa</label>
                            <select id="company_id" class="form-select">
                                <option value="">Selecione uma empresa</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="sector_id" class="form-label">Setor</label>
                            <select id="sector_id" class="form-select">
                                <option value="">Selecione o seu Setor</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card de Endereço -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Endereço</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="zip_code" class="form-label">CEP</label>
                            <div class="input-group">
                                <input type="text" id="zip_code" class="form-control cep-mask" placeholder="00000-000">
                                <span class="input-group-text d-none" id="cep-loading">
                                    <div class="spinner-border spinner-border-sm" role="status">
                                        <span class="visually-hidden">Carregando...</span>
                                    </div>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="street" class="form-label">Rua/Logradouro</label>
                            <input type="text" id="street" class="form-control" placeholder="Nome da rua">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="number" class="form-label">Número</label>
                            <input type="text" id="number" class="form-control" placeholder="123">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="complement" class="form-label">Complemento</label>
                            <input type="text" id="complement" class="form-control" placeholder="Apto, Bloco, etc.">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="bairro" class="form-label">Bairro</label>
                            <input type="text" id="bairro" class="form-control" placeholder="Nome do bairro">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="city" class="form-label">Cidade</label>
                            <input type="text" id="city" class="form-control" placeholder="Nome da cidade">
                        </div>
                        <div class="col-md-1 mb-3">
                            <label for="state" class="form-label">UF</label>
                            <input type="text" id="state" class="form-control" placeholder="SP" maxlength="2">
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>Salvar e Continuar
                </button>
            </div>
        </form>
    </div>

    <!-- Passo 2: Categorias e Produtos -->
    <div id="step2" style="display:none;">
        <h4 class="mb-4">Veja abaixo o exemplo de produtos por tempo de admissão</h4>

        <div class="row">
            @foreach($categories as $category)
            <div class="col-md-4 mb-4">
                <div class="card category-card" data-bs-toggle="collapse" data-bs-target="#collapse{{ $category->id }}" aria-expanded="false" aria-controls="collapse{{ $category->id }}" data-category-id="{{ $category->id }}">
                    <div class="card-header" id="heading{{ $category->id }}">
                        <h5 class="mb-0 d-flex justify-content-between align-items-center">
                            {{ $category->category_name }}
                            <i class="fas fa-chevron-down"></i>
                        </h5>
                    </div>

                    <div id="collapse{{ $category->id }}" class="collapse" aria-labelledby="heading{{ $category->id }}">
                        <div class="card-body p-0">
                            <div class="products-container" id="products-{{ $category->id }}">
                                <div class="text-center py-3">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Carregando...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <div class="d-flex justify-content-between mt-4">
            <button id="backToStep1" class="btn btn-secondary">Voltar</button>
            <button id="goToStep3" class="btn btn-primary">Continuar</button>
        </div>
    </div>

    <!-- Passo 3: Itens do Pedido -->
    <div id="step3" style="display:none;">
        <form id="itensForm">
            <input type="hidden" id="order_id" name="order_id">

            <div class="mb-4">
                <h4 class="text-center mb-4">
                    <i class="fas fa-gift me-2 text-primary"></i>
                    Escolha seus desejos
                </h4>
                <p class="text-muted text-center">Preencha as informações dos produtos que você deseja receber</p>
            </div>

            @for ($i = 1; $i <= 3; $i++)
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Desejo {{ $i }}
                        @if($i == 1) <span class="badge bg-danger ms-2">Obrigatório</span> @endif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="items[{{ $i }}][link]" class="form-label">
                                Link do Produto <span class="text-danger">*</span>
                            </label>
                            <input type="url" name="items[{{ $i }}][link]" class="form-control"
                                   placeholder="https://exemplo.com/produto" required>
                            <div class="form-text">Cole aqui o link do produto que você deseja</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="items[{{ $i }}][size]" class="form-label">Tamanho</label>
                            <input type="text" name="items[{{ $i }}][size]" class="form-control"
                                   placeholder="P, M, G, XG, etc.">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="items[{{ $i }}][voltage]" class="form-label">Voltagem</label>
                            <select name="items[{{ $i }}][voltage]" class="form-control">
                                <option value="">Selecione a Voltagem</option>
                                <option value="N/A">Não se aplica</option>
                                <option value="110V">110V</option>
                                <option value="220V">220V</option>
                                <option value="Bivolt">Bivolt</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="items[{{ $i }}][model_or_brand]" class="form-label">Marca/Modelo</label>
                            <input type="text" name="items[{{ $i }}][model_or_brand]" class="form-control"
                                   placeholder="Samsung, Apple, Nike, etc.">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="items[{{ $i }}][session_id]" class="form-label">Departamento</label>
                            <select name="items[{{ $i }}][session_id]" id="items[{{ $i }}][session_id]" class="form-select">
                                <option value="">Selecione um Departamento</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="items[{{ $i }}][value]" class="form-label money-mask">Valor Aproximado</label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="text" name="items[{{ $i }}][value]" class="form-control money-mask"
                                       placeholder="0,00">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="items[{{ $i }}][description]" class="form-label">Descrição</label>
                            <textarea name="items[{{ $i }}][description]" class="form-control" rows="3"
                                      placeholder="Descreva detalhes importantes sobre o produto..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            @endfor

            <div class="d-flex justify-content-between mt-4">
                <button type="button" id="backToStep2" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>Voltar
                </button>
                <button type="submit" class="btn btn-success btn-lg">
                    <i class="fas fa-check me-2"></i>Finalizar Pedido
                </button>
            </div>
        </form>
    </div>

    <!-- Passo 4: Tela de Sucesso -->
    <div id="step4" style="display:none;">
        <div class="text-center">
            <div class="mb-5">
                <div class="success-icon mb-4">
                    <i class="fas fa-check-circle text-success"></i>
                </div>
                <h2 class="text-success mb-3">Parabéns!</h2>
                <h4 class="mb-4">Seus desejos foram enviados com sucesso!</h4>
                <p class="lead text-muted mb-4">
                    Seu pedido foi registrado e está sendo analisado pela nossa equipe.
                    Você receberá uma notificação quando houver atualizações.
                </p>
            </div>

            <div class="card mx-auto" style="max-width: 500px;">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        Informações do Pedido
                    </h5>
                    <div class="row text-start">
                        <div class="col-6">
                            <strong>Número do Pedido:</strong>
                        </div>
                        <div class="col-6">
                            <span id="order-number">#000</span>
                        </div>
                        <div class="col-6">
                            <strong>Data:</strong>
                        </div>
                        <div class="col-6">
                            <span id="order-date">-</span>
                        </div>
                        <div class="col-6">
                            <strong>Status:</strong>
                        </div>
                        <div class="col-6">
                            <span class="badge bg-warning">Pendente</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-5">
                <button type="button" class="btn btn-primary btn-lg" onclick="location.reload()">
                    <i class="fas fa-home me-2"></i>Fazer Novo Pedido
                </button>
            </div>
        </div>
    </div>

    <!-- Modal para pedido existente -->
    <div class="modal fade" id="existingOrderModal" tabindex="-1" aria-labelledby="existingOrderModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="existingOrderModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>Pedido Existente
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-clipboard-list fa-3x text-warning mb-3"></i>
                        <h6 id="existingOrderMessage">Você já possui um pedido registrado.</h6>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6"><strong>Status:</strong></div>
                                <div class="col-6"><span id="existingOrderStatus" class="badge">-</span></div>
                                <div class="col-6"><strong>Data:</strong></div>
                                <div class="col-6"><span id="existingOrderDate">-</span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Entendi</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para CEP inválido -->
    <div class="modal fade" id="invalidCepModal" tabindex="-1" aria-labelledby="invalidCepModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="invalidCepModalLabel">
                        <i class="fas fa-map-marker-alt me-2"></i>CEP Inválido
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-times-circle fa-3x text-danger mb-3"></i>
                        <h6 id="invalidCepMessage">CEP não encontrado ou inválido.</h6>
                        <p class="text-muted">Por favor, verifique o CEP digitado e tente novamente.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Corrigir CEP</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para exibir imagem do produto -->
    <div class="modal fade" id="productModal" tabindex="-1" aria-labelledby="productModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productModalLabel">Nome do Produto</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="productModalImage" src="" alt="Imagem do Produto" class="img-fluid">
                    <div id="noImagePlaceholder" class="no-image-large d-none">
                        <i class="fas fa-image fa-4x text-muted"></i>
                        <p class="mt-3">Imagem não disponível</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function () {
    // Aplicar máscaras aos campos
    $('.cpf-mask').inputmask('999.999.999-99', {
        onincomplete: function() {
            $('#errorCpf').text('CPF incompleto. Por favor, digite todos os números.');
        },
        oncomplete: function() {
            // Chamar a verificação quando o CPF estiver completo
            let cpf = $(this).val();
            verificarCPF(cpf);
        }
    });

    // Máscara para telefone com detecção automática de celular (9 dígitos) ou fixo (8 dígitos)
    $('.phone-mask').inputmask({
        mask: ['(99) 9999-9999', '(99) 99999-9999'],
        keepStatic: true,
        showMaskOnHover: false
    });

    // Máscara para CEP com autocomplete
    $('.cep-mask').inputmask('99999-999', {
        oncomplete: function() {
            // Chamar a busca do CEP quando estiver completo
            let cep = $(this).val();
            buscarCEP(cep);
        }
    });

    // Função para buscar CEP na API do ViaCEP
    function buscarCEP(cep) {
        // Remover caracteres não numéricos
        const cepLimpo = cep.replace(/[^\d]/g, '');

        // Verificar se o CEP tem 8 dígitos
        if (cepLimpo.length !== 8) {
            return;
        }

        // Mostrar indicador de carregamento
        $('#cep-loading').removeClass('d-none');
        $('#street, #bairro, #city, #state').prop('disabled', true).val('');

        // Fazer a requisição para a API do ViaCEP
        $.ajax({
            url: `https://viacep.com.br/ws/${cepLimpo}/json/`,
            method: 'GET',
            dataType: 'json',
            success: function(data) {
                // Esconder indicador de carregamento
                $('#cep-loading').addClass('d-none');

                // Verificar se o CEP foi encontrado
                if (data.erro) {
                    // Mostrar modal de CEP inválido usando jQuery
                    $('#invalidCepMessage').text('CEP não encontrado.');
                    $('#invalidCepModal').modal('show');

                    $('#street, #bairro, #city, #state').prop('disabled', false).val('');
                    return;
                }

                // Preencher os campos com os dados retornados
                $('#street').val(data.logradouro || '').prop('disabled', false);
                $('#bairro').val(data.bairro || '').prop('disabled', false);
                $('#city').val(data.localidade || '').prop('disabled', false);
                $('#state').val(data.uf || '').prop('disabled', false);

                // Focar no campo número se a rua foi preenchida
                if (data.logradouro) {
                    $('#number').focus();
                } else {
                    // Se não há logradouro, focar no campo de rua para o usuário preencher
                    $('#street').focus();
                }
            },
            error: function() {
                // Esconder indicador de carregamento
                $('#cep-loading').addClass('d-none');

                // Mostrar modal de erro de conexão usando jQuery
                $('#invalidCepMessage').text('Erro ao buscar o CEP. Verifique sua conexão com a internet.');
                $('#invalidCepModal').modal('show');

                $('#street, #bairro, #city, #state').prop('disabled', false).val('');
            }
        });
    }

    // Função para validar CPF
    function validarCPF(cpf) {
        cpf = cpf.replace(/[^\d]+/g, ''); // Remove caracteres não numéricos

        if (cpf.length !== 11) return false;

        // Verifica se todos os dígitos são iguais
        if (/^(\d)\1+$/.test(cpf)) return false;

        // Validação do primeiro dígito verificador
        let soma = 0;
        for (let i = 0; i < 9; i++) {
            soma += parseInt(cpf.charAt(i)) * (10 - i);
        }
        let resto = 11 - (soma % 11);
        let dv1 = resto > 9 ? 0 : resto;

        if (dv1 !== parseInt(cpf.charAt(9))) return false;

        // Validação do segundo dígito verificador
        soma = 0;
        for (let i = 0; i < 10; i++) {
            soma += parseInt(cpf.charAt(i)) * (11 - i);
        }
        resto = 11 - (soma % 11);
        let dv2 = resto > 9 ? 0 : resto;

        if (dv2 !== parseInt(cpf.charAt(10))) return false;

        return true;
    }

    // Função para verificar CPF e carregar os dados
    function verificarCPF(cpf) {
        // Limpar mensagem de erro anterior
        $('#errorCpf').text('');

        // Validar o CPF antes de enviar a requisição
        const cpfLimpo = cpf.replace(/[^\d]+/g, '');
        if (!validarCPF(cpfLimpo)) {
            $('#errorCpf').text('CPF inválido. Por favor, verifique os números digitados.');
            return;
        }

        // Mostrar indicador de carregamento
        $('#errorCpf').html('<span class="text-info">Verificando CPF...</span>');

        $.ajax({
            url: '/verificar-pedido',
            method: 'POST',
            headers: { 'X-CSRF-TOKEN': '{{ csrf_token() }}' },
            data: { cpf: cpf },
            success: function (data) {
                // Limpar indicador de carregamento
                $('#errorCpf').text('');

                if (data.error) {
                    // Mostrar modal de pedido existente em vez de texto no errorCpf
                    if (data.order_status && data.order_date) {
                        let statusText = data.order_status === 'pending' ? 'pendente' : 'aprovado';
                        let statusClass = data.order_status === 'pending' ? 'bg-warning' : 'bg-success';
                        let orderDate = new Date(data.order_date).toLocaleDateString('pt-BR');

                        $('#existingOrderMessage').text(data.error);
                        $('#existingOrderStatus').removeClass('bg-warning bg-success bg-danger').addClass(statusClass).text(statusText.toUpperCase());
                        $('#existingOrderDate').text(orderDate);

                        // Mostrar modal usando jQuery
                        $('#existingOrderModal').modal('show');

                        // Limpar CPF quando modal for fechado (usar one para evitar múltiplos eventos)
                        $('#existingOrderModal').one('hidden.bs.modal', function () {
                            $('#cpf').val('').prop('disabled', false);
                            $('#errorCpf').text('');
                        });
                    } else {
                        $('#errorCpf').text(data.error);
                    }
                } else {
                    $('#cpf').prop('disabled', true);
                    $('#employee_id').val(data.employee.id);
                    $('#name').val(data.employee.name);
                    $('#phone').val(data.employee.phone || data.employee.fone || '');
                    $('.cpf-mask').val(data.employee.cpf);
                    $('#date_of_admission').val(data.employee.date_of_admission);
                    $('#birthday_date').val(data.employee.birthday_date);
                    $('#email').val(data.employee.email);
                    $('#zip_code').val(data.employee.zip_code);
                    $('#street').val(data.employee.street);
                    $('#number').val(data.employee.number);
                    $('#complement').val(data.employee.complement);
                    $('#bairro').val(data.employee.bairro || data.employee.neighborhood || '');
                    $('#city').val(data.employee.city);
                    $('#state').val(data.employee.state);

                    // Preencher empresas
                    $('#company_id').empty().append('<option value="">Selecione uma empresa</option>');
                    $.each(data.companies, function (index, company) {
                        $('#company_id').append(new Option(company.company_name, company.id));
                    });

                    // Preencher setores
                    $('#sector_id').empty().append('<option value="">Selecione o seu Setor</option>');
                    $.each(data.sectors, function (index, sector) {
                        $('#sector_id').append(new Option(sector.sector, sector.id));
                    });

                    // Selecionar empresa e setor caso já existam (aguardar um pouco para garantir que as opções foram carregadas)
                    setTimeout(function() {
                        if (data.employee.company_id) {
                            $('#company_id').val(data.employee.company_id);
                        }
                        if (data.employee.sector_id) {
                            $('#sector_id').val(data.employee.sector_id);
                        }
                    }, 100);

                    // Se há um pedido rejeitado, mostrar mensagem informativa
                    if (data.existing_order && data.existing_order.status === 'rejected') {
                        $('#errorCpf').removeClass('text-danger').addClass('text-warning')
                            .text('Seu pedido anterior foi rejeitado. Você pode fazer um novo pedido.');
                    }
                }
            },
            error: function () {
                $('#errorCpf').text('Erro ao verificar o CPF.');
            }
        });
    }

    // Manter o evento blur como fallback
    $('#cpf').blur(function() {
        // Verificar se o CPF está completo (com 14 caracteres incluindo pontos e traço)
        if ($(this).val().length === 14) {
            verificarCPF($(this).val());
        }
    });

    // Detectar quando o usuário cola um CPF
    $('#cpf').on('paste', function(e) {
        // Usar setTimeout para permitir que o valor seja colado antes de verificar
        setTimeout(function() {
            const cpfValue = $('#cpf').val();
            // Se o CPF colado já estiver formatado ou tiver 11 dígitos
            if (cpfValue.length === 14 || (cpfValue.length === 11 && /^\d+$/.test(cpfValue))) {
                verificarCPF(cpfValue);
            }
        }, 100);
    });

    // Detectar quando o usuário cola um CEP ou sai do campo
    $('#zip_code').on('blur paste', function(e) {
        if (e.type === 'paste') {
            // Para evento de colar, aguardar um pouco para o valor ser colado
            setTimeout(function() {
                const cepValue = $('#zip_code').val();
                if (cepValue.length === 9 || (cepValue.length === 8 && /^\d+$/.test(cepValue))) {
                    buscarCEP(cepValue);
                }
            }, 100);
        } else {
            // Para evento blur, verificar imediatamente
            const cepValue = $(this).val();
            if (cepValue.length === 9) {
                buscarCEP(cepValue);
            }
        }
    });

    // Salvar Etapa 1 (Dados do Funcionário)
    $('#etapa1Form').submit(function (e) {
        e.preventDefault();

        let formData = {
            employee_id: $('#employee_id').val(),
            phone: $('#phone').val(),
            fone: $('#phone').val(), // Manter compatibilidade
            email: $('#email').val(),
            company_id: $('#company_id').val(),
            sector_id: $('#sector_id').val(),
            date_of_admission: $('#date_of_admission').val(),
            birthday_date: $('#birthday_date').val(),
            zip_code: $('#zip_code').val(),
            street: $('#street').val(),
            number: $('#number').val(),
            complement: $('#complement').val(),
            bairro: $('#bairro').val(),
            city: $('#city').val(),
            state: $('#state').val(),
        };

        $.ajax({
            url: '/salvar-etapa1',
            method: 'POST',
            headers: { 'X-CSRF-TOKEN': '{{ csrf_token() }}' },
            data: formData,
            success: function (data) {
                $('#step1').hide();
                $('#step2').show();
                updateProgressIndicator(2);
                $('#order_id').val(data.order_id);

                // Armazenar departamentos para uso posterior
                window.departaments = data.departaments;
            },
            error: function () {
                alert('Erro ao salvar os dados do funcionário.');
            }
        });
    });

    // Inicializar o Bootstrap Collapse com evento
    $('.collapse').on('show.bs.collapse', function() {
        // Quando o acordeão abre, muda o ícone para cima
        $(this).closest('.category-card').find('i').removeClass('fa-chevron-down').addClass('fa-chevron-up');
    }).on('hide.bs.collapse', function() {
        // Quando o acordeão fecha, muda o ícone para baixo
        $(this).closest('.category-card').find('i').removeClass('fa-chevron-up').addClass('fa-chevron-down');
    });

    // Carregar produtos quando o acordeão é aberto
    $(document).on('click', '.category-card', function() {
        const categoryId = $(this).data('category-id');
        const productsContainer = $(`#products-${categoryId}`);

        // Verificar se o collapse está abrindo
        const isOpening = !$(`#collapse${categoryId}`).hasClass('show');

        // Se estiver fechando, não precisamos fazer nada
        if (!isOpening) {
            return;
        }

        // Verificar se os produtos já foram carregados
        if (productsContainer.data('loaded')) {
            return;
        }

        // Carregar produtos da categoria
        $.ajax({
            url: '/produtos-por-categoria',
            method: 'GET',
            data: { category_id: categoryId },
            success: function(data) {
                let productsHtml = '';

                if (data.products.length === 0) {
                    productsHtml = '<p class="text-center py-3">Nenhum produto encontrado nesta categoria.</p>';
                } else {
                    productsHtml = '<table class="table table-hover mb-0">';
                    productsHtml += '<tbody>';
                    $.each(data.products, function(index, product) {
                        productsHtml += `
                            <tr class="product-row" data-bs-toggle="modal" data-bs-target="#productModal" data-product-id="${product.id}" data-product-name="${product.product_name}" data-product-image="${product.image || ''}" onclick="event.stopPropagation();">
                                <td class="product-image-cell" width="80">
                                    ${product.image
                                        ? `<img src="/storage/${product.image}" alt="${product.product_name}" class="img-thumbnail product-img" style="width: 60px; height: 60px; object-fit: cover;">`
                                        : `<div class="no-image"><i class="fas fa-image text-muted"></i></div>`
                                    }
                                </td>
                                <td>
                                    <div class="product-name">${product.product_name}</div>
                                </td>
                            </tr>
                        `;
                    });
                    productsHtml += '</tbody></table>';
                }

                productsContainer.html(productsHtml);
                productsContainer.data('loaded', true);
            },
            error: function() {
                productsContainer.html('<p class="text-danger py-3 text-center">Erro ao carregar produtos.</p>');
            }
        });
    });

    // Configurar o modal de produto
    $('#productModal').on('show.bs.modal', function (event) {
        const button = $(event.relatedTarget); // Elemento que acionou o modal
        const productName = button.data('product-name');
        const productImage = button.data('product-image');

        // Atualizar título do modal
        $('#productModalLabel').text(productName);

        // Verificar se há imagem
        if (productImage) {
            $('#productModalImage').attr('src', '/storage/' + productImage).removeClass('d-none');
            $('#noImagePlaceholder').addClass('d-none');
        } else {
            $('#productModalImage').addClass('d-none');
            $('#noImagePlaceholder').removeClass('d-none');
        }
    });

    // Garantir que o clique na linha do produto não feche o acordeão
    $(document).on('click', '.product-row', function(e) {
        e.stopPropagation();
    });

    // Função para atualizar o indicador de progresso
    function updateProgressIndicator(currentStep) {
        // Remover classes ativas e completadas
        $('.step-indicator').removeClass('active completed');
        $('.progress-line').removeClass('completed');

        // Marcar etapas anteriores como completadas
        for (let i = 1; i < currentStep; i++) {
            $(`#step-indicator-${i}`).addClass('completed');
            if (i < 3) {
                $(`.progress-line:nth-child(${i * 2})`).addClass('completed');
            }
        }

        // Marcar etapa atual como ativa
        $(`#step-indicator-${currentStep}`).addClass('active');
    }

    // Navegação entre etapas
    $('#backToStep1').click(function() {
        $('#step2').hide();
        $('#step1').show();
        updateProgressIndicator(1);
    });

    $('#goToStep3').click(function() {
        $('#step2').hide();
        $('#step3').show();
        updateProgressIndicator(3);

        // Preencher departamentos
        for (let i = 1; i <= 3; i++) {
            let selectId = `#items\\[${i}\\]\\[session_id\\]`;
            let selectElement = $(selectId);
            selectElement.empty().append('<option value="">Selecione um Departamento</option>');
            $.each(window.departaments, function (index, departament) {
                selectElement.append(new Option(departament.name, departament.id));
            });
        }
    });

    $('#backToStep2').click(function() {
        $('#step3').hide();
        $('#step2').show();
        updateProgressIndicator(2);
    });

    // Salvar Itens do Pedido (Etapa 3)
    $('#itensForm').submit(function (e) {
        e.preventDefault();

        // Validar se todos os 3 desejos foram preenchidos
        let allFieldsFilled = true;
        let emptyFields = [];

        for (let i = 1; i <= 3; i++) {
            let link = $('input[name="items[' + i + '][link]"]').val().trim();
            if (!link) {
                allFieldsFilled = false;
                emptyFields.push(`Desejo ${i}`);
            }
        }

        if (!allFieldsFilled) {
            alert('Por favor, preencha o link de todos os 3 desejos:\n' + emptyFields.join(', '));
            return;
        }

        let formData = $(this).serialize(); // Serializa todos os campos automaticamente
        let items = {};

        // Obter os valores dos campos de cada desejo
        for (let i = 1; i <= 3; i++) {
            let item = {
                link: $('input[name="items[' + i + '][link]"]').val().trim(),
                size: $('input[name="items[' + i + '][size]"]').val(),
                voltage: $('input[name="items[' + i + '][voltage]"]').val(),
                model_or_brand: $('input[name="items[' + i + '][model_or_brand]"]').val(),
                session_id: $('select[name="items[' + i + '][session_id]"]').val(),
                description: $('textarea[name="items[' + i + '][description]"]').val(),
                value: $('input[name="items[' + i + '][value]"]').val()
            };

            items[i] = item;
        }

        formData += '&' + $.param({ items: items });

        // Mostrar indicador de carregamento
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Enviando...');

        $.ajax({
            url: '/salvar-itens',
            method: 'POST',
            headers: { 'X-CSRF-TOKEN': '{{ csrf_token() }}' },
            data: formData,
            success: function (response) {
                if (response.success) {
                    // Mostrar tela de sucesso
                    $('#step3').hide();
                    $('#step4').show();

                    // Preencher informações do pedido
                    $('#order-number').text('#' + response.order_id);
                    $('#order-date').text(new Date().toLocaleDateString('pt-BR'));
                } else {
                    alert(response.message || 'Erro ao salvar os itens do pedido.');
                }
            },
            error: function (xhr) {
                let errorMessage = 'Erro ao salvar os itens do pedido.';

                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    // Tratar erros de validação
                    let errors = [];
                    for (let field in xhr.responseJSON.errors) {
                        errors = errors.concat(xhr.responseJSON.errors[field]);
                    }
                    errorMessage = errors.join('\n');
                }

                alert(errorMessage);
            },
            complete: function() {
                // Restaurar botão
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Limpar CEP quando modal de CEP inválido for fechado
    $(document).on('hidden.bs.modal', '#invalidCepModal', function () {
        $('#zip_code').val('').focus();
    });
});
</script>

@endsection
