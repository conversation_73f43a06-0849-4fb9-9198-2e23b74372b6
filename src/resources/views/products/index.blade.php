@extends('layouts.app')

@section('styles')
<link href="{{ asset('css/products.css') }}" rel="stylesheet">
@endsection

@section('content')
<div class="container-fluid px-4">
    <!-- Cabeçalho -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="display-5 fw-bold mb-2" style="color: #141414;">
                <i class="fas fa-box me-3" style="color: #b1904e;"></i>Catálogo de Produtos
            </h1>
            <p class="lead text-muted mb-0">Gerencie todos os produtos disponíveis no sistema</p>
        </div>
        <div>
            <a href="{{ route('products.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Novo Produto
            </a>
        </div>
    </div>

    <!-- Filtros -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ route('products.index') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">
                        <i class="fas fa-search me-1"></i>Buscar Produto
                    </label>
                    <input type="text" name="search" class="form-control" placeholder="Digite o nome do produto..." value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">
                        <i class="fas fa-tags me-1"></i>Categoria
                    </label>
                    <select name="category" class="form-select">
                        <option value="">Todas as categorias</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                {{ $category->category_name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i>Filtrar
                    </button>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <a href="{{ route('products.index') }}" class="btn btn-secondary w-100">
                        <i class="fas fa-times me-1"></i>Limpar
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Alertas -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Tabela de Produtos -->
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-modern mb-0">
                    <thead>
                        <tr>
                            <th width="10%">
                                <i class="fas fa-image me-2"></i>Imagem
                            </th>
                            <th width="40%">
                                <i class="fas fa-box me-2"></i>Nome do Produto
                            </th>
                            <th width="30%">
                                <i class="fas fa-tags me-2"></i>Categoria
                            </th>
                            <th width="20%" class="text-center">
                                <i class="fas fa-cogs me-2"></i>Ações
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($products as $product)
                            <tr class="product-row">
                                <td>
                                    <div class="product-image-container">
                                        @if($product->image)
                                            <img src="/storage/{{ $product->image }}"
                                                 alt="{{ $product->product_name }}"
                                                 class="product-image"
                                                 data-bs-toggle="modal"
                                                 data-bs-target="#imageModal{{ $product->id }}">
                                        @else
                                            <div class="product-image-placeholder">
                                                <i class="fas fa-image"></i>
                                            </div>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="product-info">
                                        <div class="product-name">{{ $product->product_name }}</div>
                                        <div class="product-id">ID: #{{ $product->id }}</div>
                                    </div>
                                </td>
                                <td>
                                    <span class="category-badge">
                                        <i class="fas fa-tag me-1"></i>
                                        {{ $product->category->category_name }}
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ route('products.show', $product) }}"
                                           class="btn btn-outline-primary btn-sm"
                                           title="Visualizar produto">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('products.edit', $product) }}"
                                           class="btn btn-outline-secondary btn-sm"
                                           title="Editar produto">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('products.destroy', $product) }}"
                                              method="POST"
                                              class="d-inline"
                                              onsubmit="return confirm('Tem certeza que deseja excluir este produto?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                    class="btn btn-outline-danger btn-sm"
                                                    title="Excluir produto">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>

                            <!-- Modal de Imagem -->
                            @if($product->image)
                                <div class="modal fade" id="imageModal{{ $product->id }}" tabindex="-1">
                                    <div class="modal-dialog modal-lg modal-dialog-centered">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">{{ $product->product_name }}</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body text-center">
                                                <img src="/storage/{{ $product->image }}"
                                                     alt="{{ $product->product_name }}"
                                                     class="img-fluid rounded">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @empty
                            <tr>
                                <td colspan="4">
                                    <div class="empty-state">
                                        <div class="empty-icon">
                                            <i class="fas fa-box-open"></i>
                                        </div>
                                        <h4>Nenhum produto encontrado</h4>
                                        <p>Não há produtos cadastrados ou que correspondam aos filtros aplicados.</p>
                                        <a href="{{ route('products.create') }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>Cadastrar Primeiro Produto
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Paginação -->
    @if($products->hasPages())
        <div class="d-flex justify-content-center mt-4">
            {{ $products->links() }}
        </div>
    @endif
</div>
@endsection

@section('scripts')
<!-- Scripts específicos da página -->
@endsection
