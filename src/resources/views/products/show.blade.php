@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('Detalhes do Produto') }}</span>
                    <div>
                        <a href="{{ route('products.edit', $product) }}" class="btn btn-warning btn-sm me-2">
                            <i class="fas fa-edit"></i> Editar
                        </a>
                        <a href="{{ route('products.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Voltar
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="text-center mb-4">
                                <!-- Tente usar o caminho direto para a imagem -->
                                @if($product->image)
                                    <img src="/storage/{{ $product->image }}" alt="{{ $product->product_name }}" class="img-fluid rounded" style="max-height: 300px;">
                                @else
                                    <div class="border rounded p-5 text-center text-muted">
                                        <i class="fas fa-image fa-5x mb-3"></i>
                                        <p>Sem imagem disponível</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h3 class="mb-3">{{ $product->product_name }}</h3>

                            <div class="mb-3">
                                <h5 class="text-muted">Categoria</h5>
                                <p class="fs-5">{{ $product->category->category_name }}</p>
                            </div>

                            <div class="mb-3">
                                <h5 class="text-muted">Valor</h5>
                                <p class="fs-4 fw-bold">
                                    @if($product->value)
                                        R$ {{ number_format($product->value, 2, ',', '.') }}
                                    @else
                                        <span class="text-muted">Não informado</span>
                                    @endif
                                </p>
                            </div>

                            <div class="mb-3">
                                <h5 class="text-muted">Cadastrado em</h5>
                                <p>{{ $product->created_at->format('d/m/Y H:i') }}</p>
                            </div>

                            <div class="mb-3">
                                <h5 class="text-muted">Última atualização</h5>
                                <p>{{ $product->updated_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 d-flex justify-content-between">
                        <form action="{{ route('products.destroy', $product) }}" method="POST" onsubmit="return confirm('Tem certeza que deseja excluir este produto?');">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Excluir Produto
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- Scripts específicos da página -->
@endsection
