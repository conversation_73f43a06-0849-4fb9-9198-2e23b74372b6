@extends('layouts.app')


<link href="{{ asset('css/employees.css') }}" rel="stylesheet">


@section('content')
<div class="container-fluid px-4">
    <!-- Cabeçalho -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="display-5 fw-bold mb-2" style="color: #141414;">
                <i class="fas fa-gift me-3" style="color: #b1904e;"></i>Lista de Desejos
            </h1>
            <p class="lead text-muted mb-0">Gerencie e acompanhe todos os pedidos dos colaboradores</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" onclick="exportData()">
                <i class="fas fa-download me-2"></i>Exportar
            </button>
            <button class="btn btn-primary" onclick="refreshData()">
                <i class="fas fa-sync-alt me-2"></i>Atualizar
            </button>
        </div>
    </div>

    <!-- Card de Estatísticas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card bg-gradient-primary">
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">{{ $employees->total() }}</h3>
                    <p class="stats-label">Total de Colaboradores</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-gradient-success">
                <div class="stats-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number stats-number-success">{{ $employees->where('orders.0.status', 'approved')->count() }}</h3>
                    <p class="stats-label">Pedidos Aprovados</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-gradient-warning">
                <div class="stats-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number stats-number-warning">{{ $employees->where('orders.0.status', 'pending')->count() }}</h3>
                    <p class="stats-label">Pedidos Pendentes</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-gradient-danger">
                <div class="stats-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number stats-number-danger">{{ $employees->where('orders.0.status', 'reproved')->count() }}</h3>
                    <p class="stats-label">Pedidos Reprovados</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Card de Filtros -->
    <div class="card filter-card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>Filtros e Busca
            </h5>
        </div>
        <div class="card-body">

            <form method="GET" action="{{ route('employees.index') }}" id="filterForm">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-search me-1"></i>Buscar
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" name="search" class="form-control"
                                   placeholder="Nome, CPF ou chave do colaborador"
                                   value="{{ request('search') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-flag me-1"></i>Status
                        </label>
                        <select name="status" class="form-select">
                            <option value="">Todos os Status</option>
                            <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>
                                <i class="fas fa-check"></i> Aprovado
                            </option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>
                                <i class="fas fa-clock"></i> Pendente
                            </option>
                            <option value="reproved" {{ request('status') == 'reproved' ? 'selected' : '' }}>
                                <i class="fas fa-times"></i> Reprovado
                            </option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-list me-1"></i>Por Página
                        </label>
                        <select name="per_page" class="form-select" {{ request('show_all') ? 'disabled' : '' }}>
                            <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10 por página</option>
                            <option value="15" {{ request('per_page') == 15 ? 'selected' : '' }}>15 por página</option>
                            <option value="30" {{ request('per_page') == 30 ? 'selected' : '' }}>30 por página</option>
                            <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 por página</option>
                            <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100 por página</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-eye me-1"></i>Visualização
                        </label>
                        <div class="form-check form-switch mt-2">
                            <input class="form-check-input" type="checkbox" name="show_all"
                                   id="showAll" value="1" {{ request('show_all') ? 'checked' : '' }}
                                   onchange="togglePerPage(this)">
                            <label class="form-check-label" for="showAll">
                                Mostrar todos
                            </label>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label fw-semibold text-transparent">Ações</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary flex-fill" id="filterButton">
                                <span class="filter-text">
                                    <i class="fas fa-filter me-1"></i>Filtrar
                                </span>
                                <span class="filter-loading d-none">
                                    <i class="fas fa-spinner fa-spin me-1"></i>Filtrando...
                                </span>
                            </button>
                            <a href="{{ route('employees.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Tabela de Colaboradores -->
    <div class="table-container">
        @if($employees->count() > 0)
            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            @php
                                $columns = [
                                    'id' => ['label' => 'ID', 'icon' => 'fas fa-hashtag'],
                                    'created_at' => ['label' => 'Data do Pedido', 'icon' => 'fas fa-calendar'],
                                    'name' => ['label' => 'Nome', 'icon' => 'fas fa-user'],
                                    'key' => ['label' => 'Chave', 'icon' => 'fas fa-key'],
                                    'company_id' => ['label' => 'Empresa', 'icon' => 'fas fa-building'],
                                    'sector_id' => ['label' => 'Setor', 'icon' => 'fas fa-sitemap'],
                                    'value' => ['label' => 'Valor', 'icon' => 'fas fa-dollar-sign'],
                                    'status' => ['label' => 'Status', 'icon' => 'fas fa-flag'],
                                    'date_of_admission' => ['label' => 'Admissão', 'icon' => 'fas fa-calendar-plus'],
                                    'situation' => ['label' => 'Situação', 'icon' => 'fas fa-info-circle'],
                                ];
                            @endphp

                            @foreach ($columns as $col => $config)
                                <th class="sortable-header">
                                    <a href="{{ route('employees.index', array_merge(request()->query(), ['sort' => $col, 'direction' => request('direction') === 'asc' ? 'desc' : 'asc'])) }}"
                                       class="sort-link">
                                        <i class="{{ $config['icon'] }} me-2"></i>
                                        {{ $config['label'] }}
                                        @if (request('sort') === $col)
                                            <i class="fas fa-sort-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1 sort-indicator"></i>
                                        @else
                                            <i class="fas fa-sort ms-1 sort-indicator inactive"></i>
                                        @endif
                                    </a>
                                </th>
                            @endforeach
                            <th class="text-center text-white">
                                <i class="fas fa-star me-2 text-white"></i>Desejos Aprovados
                            </th>
                            <th class="text-center text-white">
                                <i class="fas fa-cogs me-2 text-white"></i>Ações
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($employees as $employee)
                            @php
                                $orderStatus = $employee->orders->isNotEmpty() ? $employee->orders->first()->status : null;
                                $rowClass = '';
                                switch($orderStatus) {
                                    case 'pending':
                                        $rowClass = 'row-warning';
                                        break;
                                    case 'approved':
                                        $rowClass = 'row-success';
                                        break;
                                    case 'reproved':
                                        $rowClass = 'row-danger';
                                        break;
                                    default:
                                        $rowClass = 'row-default';
                                }
                            @endphp
                            <tr class="table-row {{ $rowClass }}">
                                <td class="fw-bold text-primary">#{{ $employee->id }}</td>
                                <td>
                                    @if($employee->orders->isNotEmpty() && $employee->orders->first()->created_at)
                                        <div class="date-info">
                                            <i class="fas fa-calendar text-muted me-1"></i>
                                            {{ $employee->orders->first()->created_at->format('d/m/Y') }}
                                            <small class="text-muted d-block">{{ $employee->orders->first()->created_at->format('H:i') }}</small>
                                        </div>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="employee-info">
                                        <div class="employee-avatar-small">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="employee-details-small">
                                            <div class="employee-name-small">{{ $employee->name }}</div>
                                            <small class="text-muted">CPF: {{ $employee->cpf ?? 'Não informado' }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="key-badge">
                                        <i class="fas fa-key me-1"></i>
                                        {{ $employee->key }}
                                    </span>
                                </td>
                                <td>
                                    <div class="company-info">
                                        <i class="fas fa-building text-muted me-1"></i>
                                        {{ $employee->company->company_name ?? 'Não informada' }}
                                    </div>
                                </td>
                                <td>
                                    <div class="sector-info">
                                        <i class="fas fa-sitemap text-muted me-1"></i>
                                        {{ $employee->sector->sector ?? 'Não informado' }}
                                    </div>
                                </td>
                                <td>
                                    <span class="value-badge">
                                        <i class="fas fa-dollar-sign me-1"></i>
                                        R$ {{ number_format($employee->value, 2, ',', '.') }}
                                    </span>
                                </td>
                                <td>
                                    @if($employee->orders->isNotEmpty())
                                        @php
                                            $status = $employee->orders->first()->status;
                                            $statusConfig = [
                                                'pending' => ['class' => 'warning', 'icon' => 'clock', 'text' => 'Pendente'],
                                                'approved' => ['class' => 'success', 'icon' => 'check-circle', 'text' => 'Aprovado'],
                                                'reproved' => ['class' => 'danger', 'icon' => 'times-circle', 'text' => 'Reprovado']
                                            ];
                                            $config = $statusConfig[$status] ?? ['class' => 'secondary', 'icon' => 'question', 'text' => 'Indefinido'];
                                        @endphp
                                        <span class="status-badge status-{{ $config['class'] }}">
                                            <i class="fas fa-{{ $config['icon'] }} me-1"></i>
                                            {{ $config['text'] }}
                                        </span>
                                    @else
                                        <span class="status-badge status-secondary">
                                            <i class="fas fa-minus me-1"></i>
                                            Sem pedido
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    @if($employee->date_of_admission)
                                        <div class="date-info">
                                            <i class="fas fa-calendar-plus text-muted me-1"></i>
                                            {{ (new DateTime($employee->date_of_admission))->format('d/m/Y') }}
                                        </div>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="situation-badge situation-{{ strtolower($employee->situation) }}">
                                        {{ $employee->situation }}
                                    </span>
                                </td>
                                <td>
                                    @php
                                        $approvedItems = $employee->items()->where('status', 'approved')->get();
                                    @endphp
                                    @if($approvedItems->count() > 0)
                                        <div class="approved-wishes-table">
                                            @foreach($approvedItems as $item)
                                                <div class="wish-item-table">
                                                    <i class="fas fa-gift text-success me-1"></i>
                                                    <span>{{ Str::limit($item->description ?: 'Sem descrição', 30) }}</span>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <span class="text-muted">
                                            <i class="fas fa-minus me-1"></i>
                                            Nenhum desejo aprovado
                                        </span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    <div class="action-buttons">
                                        <a href="{{ route('employees.show', ['id' => $employee->id]) }}"
                                           class="btn btn-primary btn-sm"
                                           data-bs-toggle="tooltip"
                                           title="Ver detalhes do colaborador">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <!-- Estado Vazio -->
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>Nenhum colaborador encontrado</h3>
                <p>Não há colaboradores que correspondam aos filtros aplicados.</p>
                <a href="{{ route('employees.index') }}" class="btn btn-primary">
                    <i class="fas fa-refresh me-2"></i>Limpar Filtros
                </a>
            </div>
        @endif
    </div>
    <!-- Paginação Melhorada -->
    @if(!request('show_all') && $employees->hasPages())
        <div class="pagination-container">
            <div class="pagination-info">
                <span class="text-muted">
                    Mostrando {{ $employees->firstItem() }} a {{ $employees->lastItem() }}
                    de {{ $employees->total() }} colaboradores
                </span>
            </div>
            <div class="pagination-wrapper">
                {{ $employees->links() }}
            </div>
        </div>
    @elseif(request('show_all'))
        <div class="pagination-container">
            <div class="pagination-info text-center">
                <span class="badge bg-primary fs-6">
                    <i class="fas fa-list me-2"></i>
                    Mostrando todos os {{ $employees->total() }} colaboradores
                </span>
            </div>
        </div>
    @endif
</div>

<script>
function togglePerPage(checkbox) {
    const perPageSelect = document.querySelector('select[name="per_page"]');
    perPageSelect.disabled = checkbox.checked;
}

function exportData() {
    alert('Funcionalidade de exportação será implementada em breve!');
}

function refreshData() {
    window.location.reload();
}

$(document).ready(function () {
    // Inicializar tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Configurar toggle de paginação
    togglePerPage(document.getElementById('showAll'));

    // Spinner no botão de filtrar
    $('#filterForm').on('submit', function() {
        const filterButton = $('#filterButton');
        const filterText = filterButton.find('.filter-text');
        const filterLoading = filterButton.find('.filter-loading');

        filterText.addClass('d-none');
        filterLoading.removeClass('d-none');
        filterButton.prop('disabled', true);
    });

    // Auto-submit do formulário quando filtros mudarem
    $('#filterForm select, #filterForm input[type="checkbox"]').on('change', function() {
        if (this.name !== 'search') {
            $('#filterForm').submit();
        }
    });

    // Debounce para campo de busca
    let searchTimeout;
    $('#filterForm input[name="search"]').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            $('#filterForm').submit();
        }, 500);
    });

    // Animação de entrada das linhas da tabela
    $('.table-row').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateX(-20px)'
        }).delay(index * 50).animate({
            'opacity': '1'
        }, 300, function() {
            $(this).css('transform', 'translateX(0)');
        });
    });
});
</script>
@endsection
