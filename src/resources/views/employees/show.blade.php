@extends('layouts.app')

@section('styles')
<link href="{{ asset('css/employees.css') }}" rel="stylesheet">
@endsection

@section('content')
<div class="container-fluid px-4">
    <!-- Cabeçalho com Breadcrumb -->
    <div class="header-section mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ route('employees.index') }}" class="breadcrumb-link">
                        <i class="fas fa-list me-1"></i>Lista de Colaboradores
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="fas fa-user me-1"></i>{{ $employee->name }}
                </li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center">
            <div class="employee-header-info">
                <h1 class="display-6 fw-bold mb-2">
                    <span class="employee-name-large">{{ $employee->name }}</span>
                </h1>
                <p class="lead text-muted mb-0">
                    <i class="fas fa-building me-2"></i>{{ $employee->company->company_name ?? 'Empresa não informada' }}
                    <span class="mx-2">•</span>
                    <i class="fas fa-sitemap me-2"></i>{{ $employee->sector->sector ?? 'Setor não informado' }}
                </p>
            </div>

            <div class="employee-status-large">
                @if($employee->orders->isNotEmpty())
                    @php
                        $status = $employee->orders->first()->status;
                        $statusConfig = [
                            'pending' => ['class' => 'warning', 'icon' => 'clock', 'text' => 'Pendente'],
                            'approved' => ['class' => 'success', 'icon' => 'check-circle', 'text' => 'Aprovado'],
                            'reproved' => ['class' => 'danger', 'icon' => 'times-circle', 'text' => 'Reprovado']
                        ];
                        $config = $statusConfig[$status] ?? ['class' => 'secondary', 'icon' => 'question', 'text' => 'Indefinido'];
                    @endphp
                    <div class="status-card status-{{ $config['class'] }}">
                        <i class="fas fa-{{ $config['icon'] }} status-icon"></i>
                        <div class="status-info">
                            <div class="status-label">Status do Pedido</div>
                            <div class="status-value">{{ $config['text'] }}</div>
                        </div>
                    </div>
                @else
                    <div class="status-card status-secondary">
                        <i class="fas fa-minus status-icon"></i>
                        <div class="status-info">
                            <div class="status-label">Status do Pedido</div>
                            <div class="status-value">Sem pedido</div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Cards de Informações -->
    <div class="row g-4 mb-4">
        <!-- Card de Informações Pessoais -->
        <div class="col-lg-6">
            <div class="info-card">
                <div class="info-card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-circle me-2"></i>Informações Pessoais
                    </h5>
                </div>
                <div class="info-card-body">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-user text-muted me-2"></i>Nome Completo
                            </div>
                            <div class="info-value">{{ $employee->name }}</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-id-card text-muted me-2"></i>CPF
                            </div>
                            <div class="info-value">{{ $employee->cpf ?? 'Não informado' }}</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-envelope text-muted me-2"></i>E-mail
                            </div>
                            <div class="info-value">{{ $employee->email ?? 'Não informado' }}</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-phone text-muted me-2"></i>Telefone
                            </div>
                            <div class="info-value">{{ $employee->fone ?? 'Não informado' }}</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-key text-muted me-2"></i>Chave
                            </div>
                            <div class="info-value">
                                <span class="key-badge-large">{{ $employee->key }}</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-info-circle text-muted me-2"></i>Situação
                            </div>
                            <div class="info-value">
                                <span class="situation-badge-large">{{ $employee->situation }}</span>
                                <div class="mt-2">
                                    <form action="{{ route('employees.update-situation', $employee->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('PATCH')
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="desligado" value="1"
                                                   id="desligadoCheck" {{ $employee->situation === 'DESLIGADO' ? 'checked' : '' }}
                                                   onclick="return confirm('Tem certeza que deseja marcar como DESLIGADO?')"
                                                   onchange="this.form.submit()">
                                            <label class="form-check-label text-danger fw-bold" for="desligadoCheck">
                                                <i class="fas fa-user-times me-1"></i>Marcar como DESLIGADO
                                            </label>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-calendar-plus text-muted me-2"></i>Data de Admissão
                            </div>
                            <div class="info-value">
                                {{ $employee->date_of_admission ? (new DateTime($employee->date_of_admission))->format('d/m/Y') : 'Não informado' }}
                                @if($employee->date_of_admission)
                                    @php
                                        $admissionDate = new DateTime($employee->date_of_admission);
                                        $now = new DateTime();
                                        $interval = $admissionDate->diff($now);
                                        $years = $interval->y;
                                        $months = $interval->m;
                                    @endphp
                                    <div class="time-badge">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ $years > 0 ? $years . ' ' . ($years == 1 ? 'ano' : 'anos') : '' }}
                                        {{ $years > 0 && $months > 0 ? ' e ' : '' }}
                                        {{ $months > 0 ? $months . ' ' . ($months == 1 ? 'mês' : 'meses') : '' }}
                                        {{ $years == 0 && $months == 0 ? 'Menos de 1 mês' : '' }}
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-birthday-cake text-muted me-2"></i>Data de Nascimento
                            </div>
                            <div class="info-value">{{ $employee->birthday_date ? (new DateTime($employee->birthday_date))->format('d/m/Y') : 'Não informado' }}</div>
                        </div>

                        <div class="info-item full-width">
                            <div class="info-label">
                                <i class="fas fa-map-marker-alt text-muted me-2"></i>Endereço Completo
                            </div>
                            <div class="info-value">
                                @if($employee->street || $employee->city)
                                    {{ $employee->street }}{{ $employee->number ? ', ' . $employee->number : '' }}{{ $employee->complement ? ' - ' . $employee->complement : '' }}{{ $employee->bairro ? ' - ' . $employee->bairro : '' }}{{ $employee->city ? ' - ' . $employee->city : '' }}{{ $employee->state ? ' - ' . $employee->state : '' }}{{ $employee->zip_code ? ' - CEP: ' . $employee->zip_code : '' }}
                                @else
                                    Endereço não informado
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Card de Informações da Empresa e Pedido -->
        <div class="col-lg-6">
            <div class="info-card">
                <div class="info-card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>Informações da Empresa e Pedido
                    </h5>
                </div>
                <div class="info-card-body">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-building text-muted me-2"></i>Empresa
                            </div>
                            <div class="info-value">{{ $employee->company->company_name ?? 'Não informada' }}</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-sitemap text-muted me-2"></i>Setor
                            </div>
                            <div class="info-value">{{ $employee->sector->sector ?? 'Não informado' }}</div>
                        </div>

                        @if($employee->orders->isNotEmpty())
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-calendar text-muted me-2"></i>Data do Pedido
                                </div>
                                <div class="info-value">
                                    {{ $employee->orders->first()->created_at ? $employee->orders->first()->created_at->format('d/m/Y H:i') : 'Não informado' }}
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-dollar-sign text-muted me-2"></i>Valor Liberado
                                </div>
                                <div class="info-value">
                                    <span class="value-badge-large primary">R$ {{ number_format($employee->value, 2, ',', '.') }}</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-shopping-cart text-muted me-2"></i>Valor do Pedido
                                </div>
                                <div class="info-value">
                                    <span class="value-badge-large success">R$ {{ number_format($employee->orders->first()->approved_value ?? 0, 2, ',', '.') }}</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-flag text-muted me-2"></i>Status do Pedido
                                </div>
                                <div class="info-value">
                                    @php
                                        $status = $employee->orders->first()->status;
                                        $statusConfig = [
                                            'approved' => ['class' => 'success', 'icon' => 'check-circle', 'text' => 'Aprovado'],
                                            'pending' => ['class' => 'warning', 'icon' => 'clock', 'text' => 'Pendente'],
                                            'reproved' => ['class' => 'danger', 'icon' => 'times-circle', 'text' => 'Rejeitado']
                                        ];
                                        $config = $statusConfig[$status] ?? ['class' => 'secondary', 'icon' => 'question', 'text' => 'Indefinido'];
                                    @endphp
                                    <span class="status-badge-large status-{{ $config['class'] }}">
                                        <i class="fas fa-{{ $config['icon'] }} me-1"></i>
                                        {{ $config['text'] }}
                                    </span>
                                </div>
                            </div>

                            @if($employee->orders->first()->status_gcom)
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-cog text-muted me-2"></i>Status GCOM
                                    </div>
                                    <div class="info-value">{{ $employee->orders->first()->status_gcom }}</div>
                                </div>
                            @endif

                            @if($employee->orders->first()->number_gcom)
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-hashtag text-muted me-2"></i>Número GCOM
                                    </div>
                                    <div class="info-value">{{ $employee->orders->first()->number_gcom }}</div>
                                </div>
                            @endif

                            @if($employee->orders->first()->ordered_value)
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-receipt text-muted me-2"></i>Valor Comprado
                                    </div>
                                    <div class="info-value">
                                        <span class="value-badge-large info">R$ {{ number_format($employee->orders->first()->ordered_value, 2, ',', '.') }}</span>
                                    </div>
                                </div>
                            @endif
                        @else
                            <div class="info-item full-width">
                                <div class="empty-state-small">
                                    <i class="fas fa-inbox text-muted"></i>
                                    <p class="text-muted mb-0">Nenhum pedido encontrado para este colaborador.</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Seção de Desejos -->
    <div class="wishes-section">
        <div class="wishes-header">
            <h3 class="section-title">
                <i class="fas fa-star me-2"></i>Desejos do Colaborador
            </h3>
            <p class="section-subtitle">Gerencie e aprove os itens solicitados pelo colaborador</p>
        </div>

        @php
            $activeItems = $employee->items->where('status', '!=', 'reproved');
        @endphp

        @if($activeItems->count() > 0)
            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="10%">Data</th>
                            <th width="15%">Link</th>
                            <th width="25%">Descrição</th>
                            <th width="10%">Tamanho</th>
                            <th width="15%">Marca/Modelo</th>
                            <th width="10%">Voltagem</th>
                            <th width="10%">Valor</th>
                            <th width="10%">Status</th>
                            <th width="15%">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($activeItems as $index => $item)
                            <tr class="wish-row">
                                <td>
                                    <span class="wish-number-badge">#{{ $index + 1 }}</span>
                                </td>
                                <td>
                                    <div class="date-info">
                                        {{ $item->created_at->format('d/m/Y') }}
                                        <small class="text-muted d-block">{{ $item->created_at->format('H:i') }}</small>
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ $item->link }}" target="_blank" class="product-link">
                                        <i class="fas fa-external-link-alt me-1"></i>
                                        Ver Produto
                                    </a>
                                </td>
                                <td>
                                    <div class="description-cell">
                                        {{ $item->description ?: 'Não informado' }}
                                    </div>
                                </td>
                                <td>
                                    @if($item->size)
                                        <span class="size-badge">{{ $item->size }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    @if($item->model_or_brand)
                                        <span class="brand-badge">{{ $item->model_or_brand }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    @if($item->voltage)
                                        <span class="voltage-badge">{{ $item->voltage }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="price-badge">R$ {{ number_format($item->value, 2, ',', '.') }}</span>
                                </td>
                                <td>
                                    @php
                                        $statusConfig = [
                                            'approved' => ['class' => 'success', 'icon' => 'check-circle', 'text' => 'Aprovado'],
                                            'pending' => ['class' => 'warning', 'icon' => 'clock', 'text' => 'Pendente'],
                                            'reproved' => ['class' => 'danger', 'icon' => 'times-circle', 'text' => 'Reprovado']
                                        ];
                                        $config = $statusConfig[$item->status] ?? ['class' => 'secondary', 'icon' => 'question', 'text' => 'Indefinido'];
                                    @endphp
                                    <span class="status-badge status-{{ $config['class'] }}">
                                        <i class="fas fa-{{ $config['icon'] }} me-1"></i>
                                        {{ $config['text'] }}
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button type="button"
                                                class="btn btn-success btn-sm"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editModal{{ $item->id }}"
                                                title="Aprovar item">
                                            <i class="fas fa-check"></i>
                                        </button>

                                        <form action="{{ route('items.reprove', $item->id) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <input type="hidden" name="employee_id" value="{{ $employee->id }}">
                                            <button type="submit"
                                                    class="btn btn-danger btn-sm"
                                                    title="Reprovar item"
                                                    onclick="return confirm('Tem certeza que deseja reprovar este item?')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-star"></i>
                </div>
                <h4>Nenhum desejo ativo encontrado</h4>
                <p>Este colaborador não possui desejos ativos no momento.</p>
            </div>
        @endif
    </div>

    <!-- Seção de Histórico -->
    @php
        $reprovedItems = $employee->items->where('status', 'reproved');
    @endphp

    @if($reprovedItems->count() > 0)
        <div class="history-section">
            <div class="history-header">
                <h3 class="section-title">
                    <i class="fas fa-history me-2"></i>Histórico de Desejos Reprovados
                </h3>
                <p class="section-subtitle">Itens que foram reprovados anteriormente</p>
            </div>

            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="10%">Data</th>
                            <th width="20%">Link</th>
                            <th width="35%">Descrição</th>
                            <th width="15%">Tamanho</th>
                            <th width="15%">Valor</th>
                            <th width="10%">Status</th>
                            <th width="10%">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($reprovedItems as $index => $item)
                            <tr class="history-row">
                                <td>
                                    <span class="history-number-badge">#{{ $index + 1 }}</span>
                                </td>
                                <td>
                                    <div class="date-info">
                                        {{ $item->created_at->format('d/m/Y') }}
                                        <small class="text-muted d-block">{{ $item->created_at->format('H:i') }}</small>
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ $item->link }}" target="_blank" class="product-link-history">
                                        <i class="fas fa-external-link-alt me-1"></i>
                                        Ver Produto
                                    </a>
                                </td>
                                <td>
                                    <div class="description-cell">
                                        {{ $item->description ?: 'Não informado' }}
                                    </div>
                                </td>
                                <td>
                                    @if($item->size)
                                        <span class="size-badge-history">{{ $item->size }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="price-badge-history">R$ {{ number_format($item->value, 2, ',', '.') }}</span>
                                </td>
                                <td>
                                    <span class="status-badge status-danger">
                                        <i class="fas fa-times-circle me-1"></i>
                                        Reprovado
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button type="button"
                                                class="btn btn-warning btn-sm"
                                                data-bs-toggle="modal"
                                                data-bs-target="#approveAndReproveModal{{ $item->id }}"
                                                title="Aprovar este e reprovar demais">
                                            <i class="fas fa-check-double"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif

    <!-- Navegação entre Colaboradores -->
    <div class="navigation-section">
        <div class="navigation-container">
            <div class="navigation-left">
                @if ($beforeEmployee)
                    <a href="{{ route('employees.show', ['id' => $beforeEmployee->id]) }}" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>
                        <div class="nav-info">
                            <div class="nav-label">Anterior</div>
                            <div class="nav-name">{{ Str::limit($beforeEmployee->name, 20) }}</div>
                        </div>
                    </a>
                @endif
            </div>

            <div class="navigation-center">
                <a href="{{ route('employees.index') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-list me-2"></i>
                    Voltar à Lista
                </a>
            </div>

            <div class="navigation-right">
                @if ($nextEmployee)
                    <a href="{{ route('employees.show', ['id' => $nextEmployee->id]) }}" class="btn btn-outline-secondary btn-lg">
                        <div class="nav-info">
                            <div class="nav-label">Próximo</div>
                            <div class="nav-name">{{ Str::limit($nextEmployee->name, 20) }}</div>
                        </div>
                        <i class="fas fa-arrow-right ms-2"></i>
                    </a>
                @endif
            </div>
        </div>
    </div>
    </div>
</div>

<!-- Modais de Edição -->
@foreach ($employee->items as $item)
    @if($item->status != 'reproved')
    <!-- Modal de Edição para o item {{ $item->id }} -->
    <div class="modal fade" id="editModal{{ $item->id }}" tabindex="-1" aria-labelledby="editModalLabel{{ $item->id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel{{ $item->id }}">Editar Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ route('items.approve', $item->id) }}" method="POST">
                    @csrf
                    @method('PATCH')
                    <input type="hidden" name="employee_id" value="{{ $employee->id }}">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="description{{ $item->id }}" class="form-label">Descrição</label>
                            <textarea class="form-control" id="description{{ $item->id }}" name="description" rows="3">{{ $item->description }}</textarea>
                        </div>
                        <div class="mb-3">
                            <label for="size{{ $item->id }}" class="form-label">Tamanho</label>
                            <input type="text" class="form-control" id="size{{ $item->id }}" name="size" value="{{ $item->size }}">
                        </div>
                        <div class="mb-3">
                            <label for="model_or_brand{{ $item->id }}" class="form-label">Marca/Modelo</label>
                            <input type="text" class="form-control" id="model_or_brand{{ $item->id }}" name="model_or_brand" value="{{ $item->model_or_brand }}">
                        </div>
                        <div class="mb-3">
                            <label for="voltage{{ $item->id }}" class="form-label">Voltagem</label>
                            <input type="text" class="form-control" id="voltage{{ $item->id }}" name="voltage" value="{{ $item->voltage }}">
                        </div>
                        <div class="mb-3">
                            <label for="value{{ $item->id }}" class="form-label">Valor</label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="text" class="form-control money-mask" id="value{{ $item->id }}" name="value" value="{{ number_format($item->value, 2, ',', '.') }}">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="session_id{{ $item->id }}" class="form-label">Departamento</label>
                            <select class="form-control" id="session_id{{ $item->id }}" name="session_id">
                                <option value="">Selecione um Departamento</option>
                                @foreach($departaments as $departament)
                                    <option value="{{ $departament->id }}" {{ $item->session_id == $departament->id ? 'selected' : '' }}>
                                        {{ $departament->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-success"
                                onclick="return confirm('Tem certeza que deseja aprovar este item?')">Confirmar Aprovação</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endif
@endforeach

<!-- Modais de Aprovação e Reprovação dos Demais -->
@foreach ($employee->items as $item)
    <!-- Modal de Aprovação e Reprovação dos Demais para o item {{ $item->id }} -->
    <div class="modal fade" id="approveAndReproveModal{{ $item->id }}" tabindex="-1" aria-labelledby="approveAndReproveModalLabel{{ $item->id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approveAndReproveModalLabel{{ $item->id }}">
                        <i class="fas fa-check-double me-2"></i>Aprovar Item e Reprovar Demais
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ route('items.approve-and-reprove-others', $item->id) }}" method="POST">
                    @csrf
                    @method('PATCH')
                    <input type="hidden" name="employee_id" value="{{ $employee->id }}">
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Atenção!</strong> Esta ação irá:
                            <ul class="mb-0 mt-2">
                                <li>Aprovar <strong>apenas este item</strong></li>
                                <li>Reprovar <strong>todos os demais itens</strong> do colaborador</li>
                            </ul>
                        </div>

                        <div class="mb-3">
                            <label for="description_approve{{ $item->id }}" class="form-label">Descrição</label>
                            <textarea class="form-control" id="description_approve{{ $item->id }}" name="description" rows="3">{{ $item->description }}</textarea>
                        </div>
                        <div class="mb-3">
                            <label for="size_approve{{ $item->id }}" class="form-label">Tamanho</label>
                            <input type="text" class="form-control" id="size_approve{{ $item->id }}" name="size" value="{{ $item->size }}">
                        </div>
                        <div class="mb-3">
                            <label for="model_or_brand_approve{{ $item->id }}" class="form-label">Marca/Modelo</label>
                            <input type="text" class="form-control" id="model_or_brand_approve{{ $item->id }}" name="model_or_brand" value="{{ $item->model_or_brand }}">
                        </div>
                        <div class="mb-3">
                            <label for="voltage_approve{{ $item->id }}" class="form-label">Voltagem</label>
                            <input type="text" class="form-control" id="voltage_approve{{ $item->id }}" name="voltage" value="{{ $item->voltage }}">
                        </div>
                        <div class="mb-3">
                            <label for="value_approve{{ $item->id }}" class="form-label">Valor</label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="text" class="form-control money-mask" id="value_approve{{ $item->id }}" name="value" value="{{ number_format($item->value, 2, ',', '.') }}">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="session_id_approve{{ $item->id }}" class="form-label">Departamento</label>
                            <select class="form-control" id="session_id_approve{{ $item->id }}" name="session_id">
                                <option value="">Selecione um Departamento</option>
                                @foreach($departaments as $departament)
                                    <option value="{{ $departament->id }}" {{ $item->session_id == $departament->id ? 'selected' : '' }}>
                                        {{ $departament->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-warning" onclick="return confirm('Tem certeza? Esta ação irá reprovar todos os outros itens do colaborador.')">
                            <i class="fas fa-check-double me-1"></i>Aprovar Este e Reprovar Demais
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endforeach

@endsection
