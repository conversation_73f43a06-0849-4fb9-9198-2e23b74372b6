<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF <PERSON> -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Essentia') }}</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="{{ asset('css/gifthub-theme.css') }}" rel="stylesheet">

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8/jquery.inputmask.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    @vite(['resources/sass/app.scss', 'resources/js/app.js'])

    <!-- Scripts adicionais -->
    @yield('scripts')

</head>
<body class="bg-light">
    <div id="app">
        <main class="py-4">
            @yield('content')
        </main>
    </div>
</body>
</html>

<!-- Scripts adicionais para formatação monetária -->
<script>
    $(document).ready(function() {
        // Aplicar máscara para todos os campos com classe money-mask
        function applyMoneyMask() {
            $('.money-mask').inputmask({
                alias: 'numeric',
                groupSeparator: '.',
                radixPoint: ',',
                autoGroup: true,
                digits: 2,
                digitsOptional: false,
                prefix: '',
                placeholder: '0',
                rightAlign: false
            });
        }

        // Aplicar máscara inicial
        applyMoneyMask();

        // Interceptar todos os formulários que contêm campos money-mask
        $('form:has(.money-mask)').submit(function(e) {
            $('.money-mask').each(function() {
                // Criar um campo oculto com o valor formatado corretamente
                let rawValue = $(this).val().replace(/\./g, '').replace(',', '.');
                let numValue = parseFloat(rawValue);
                let formattedValue = isNaN(numValue) ? '0.00' : numValue.toFixed(2);

                // Criar ou atualizar um campo oculto com o mesmo nome
                let hiddenField = $('<input type="hidden">').attr('name', $(this).attr('name')).val(formattedValue);
                $(this).after(hiddenField);

                // Desabilitar o campo original para que não seja enviado
                $(this).prop('disabled', true);
            });
            return true;
        });

        // Para elementos adicionados dinamicamente
        $(document).on('DOMNodeInserted', function(e) {
            if ($(e.target).find('.money-mask').length > 0) {
                applyMoneyMask();
            }
        });
    });
</script>
