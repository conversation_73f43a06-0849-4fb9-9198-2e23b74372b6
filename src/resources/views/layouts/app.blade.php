<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF <PERSON> -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Essentia') }}</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8/jquery.inputmask.min.js"></script>

    @vite(['resources/sass/app.scss', 'resources/js/app.js'])

    <!-- Tema GiftHub -->
    <link href="{{ asset('css/gifthub-theme.css') }}" rel="stylesheet">

    <!-- Estilos específicos das páginas -->
    @yield('styles')

    <!-- Scripts adicionais -->
    @yield('scripts')

</head>
<body class="bg-light">
    <div id="app">
        <nav class="navbar navbar-expand-md navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand" href="{{ url('/') }}">

                            <img src="https://essentiagroup.global/static/media/ho.a335e8fe9332bf2d7218c9e8a3128627.svg"
                                alt="Imagem Centralizada"
                                class="img-fluid"
                            >

                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Left Side Of Navbar -->
                    @auth
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('meu-desejo') }}">Meu Desejo</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('employees.index') }}">Lista de Desejos</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('products.index') }}">Produtos</a>
                            </li>
                        </ul>
                    @endauth

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Authentication Links -->
                        @guest
                            @if (Route::has('login'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('login') }}">{{ __('Login') }}</a>
                                </li>
                            @endif

                            @if (Route::has('register'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('register') }}">{{ __('Register') }}</a>
                                </li>
                            @endif
                        @else
                            <li class="nav-item dropdown">
                                <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    {{ Auth::user()->name }}
                                </a>

                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="{{ route('logout') }}"
                                       onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                        {{ __('Logout') }}
                                    </a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>

        <main class="py-4">
            @yield('content')
        </main>

    </div>
</body>
</html>

<!-- Scripts adicionais para formatação monetária -->
<script>
    $(document).ready(function() {
        // Aplicar máscara para todos os campos com classe money-mask
        function applyMoneyMask() {
            $('.money-mask').inputmask({
                alias: 'numeric',
                groupSeparator: '.',
                radixPoint: ',',
                autoGroup: true,
                digits: 2,
                digitsOptional: false,
                prefix: '',
                placeholder: '0',
                rightAlign: false
            });
        }

        // Aplicar máscara inicial
        applyMoneyMask();

        // Interceptar todos os formulários que contêm campos money-mask
        $('form:has(.money-mask)').submit(function(e) {
            $('.money-mask').each(function() {
                // Criar um campo oculto com o valor formatado corretamente
                let rawValue = $(this).val().replace(/\./g, '').replace(',', '.');
                let numValue = parseFloat(rawValue);
                let formattedValue = isNaN(numValue) ? '0.00' : numValue.toFixed(2);

                // Criar ou atualizar um campo oculto com o mesmo nome
                let hiddenField = $('<input type="hidden">').attr('name', $(this).attr('name')).val(formattedValue);
                $(this).after(hiddenField);

                // Desabilitar o campo original para que não seja enviado
                $(this).prop('disabled', true);
            });
            return true;
        });

        // Para elementos adicionados dinamicamente
        $(document).on('DOMNodeInserted', function(e) {
            if ($(e.target).find('.money-mask').length > 0) {
                applyMoneyMask();
            }
        });
    });
</script>
