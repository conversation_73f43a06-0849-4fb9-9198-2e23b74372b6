<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('item_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->foreignId('employee_id')->constrained('employees')->onDelete('cascade');
            $table->foreignId('session_id')->nullable()->constrained('departaments')->onDelete('cascade');
            $table->string('link', 100);
            $table->string('description', 100)->nullable();
            $table->string('size', 50)->nullable();
            $table->string('voltage', 50)->nullable();
            $table->string('model_or_brand', 100)->nullable();
            $table->enum('status', ['pending','approved', 'reproved']);
            $table->decimal('value', 10, 2)->nullable()->default(0.00);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('item_orders');
    }
};
