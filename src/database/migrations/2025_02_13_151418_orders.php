<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->date('order_date')->nullable();
            $table->string('number_gcom', 50)->nullable();
            $table->foreignId('employee_id')->constrained('employees')->onDelete('cascade');
            $table->string('status_gcom', 50)->nullable();
            $table->enum('status', ['pending','approved', 'reproved']);
            $table->decimal('ordered_value', 10, 2)->nullable();
            $table->decimal('approved_value', 10, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
