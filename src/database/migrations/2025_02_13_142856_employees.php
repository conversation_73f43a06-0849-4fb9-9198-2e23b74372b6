<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees',function(Blueprint $table){
            $table->id();
            $table->string('name', 100);
            $table->string('cpf', 14)->unique();
            $table->string('key', 255)->unique()->nullable();
            $table->string('fone', 15)->nullable();
            $table->string('email', 100)->unique()->nullable();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->foreignId('sector_id')->constrained()->onDelete('cascade');
            $table->date('date_of_admission')->nullable();
            $table->date('birthday_date')->nullable();
            $table->decimal('value', 10, 2)->nullable();
            $table->string('street', 255)->nullable();
            $table->string('number', 6)->nullable();
            $table->string('complement', 255)->nullable();
            $table->string('zip_code', 9)->nullable();
            $table->string('bairro', 100)->nullable();
            $table->string('city', 100)->nullable();
            $table->string('state', 2)->nullable();
            $table->enum('situation', ['CLT', 'PJ', 'PJ_FREELA', 'GESTOR', 'DIRETOR', 'DESLIGADO', 'OUTROS']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
