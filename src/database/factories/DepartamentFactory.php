<?php

namespace Database\Factories;

use App\Models\Departament;
use Illuminate\Database\Eloquent\Factories\Factory;

class DepartamentFactory extends Factory
{
    protected $model = Departament::class;

    public function definition(): array
    {
        $departaments = [
            'Ar e Ventilação',
            '<PERSON>udio',
            'Automotivo',
            'Beleza & Perfumaria',
            'Brinquedos',
            'Cama, Mesa e Banho',
            'Câmeras e Drones',
            'Casa e Construção',
            'Celulares e Smartphones',
            'Colchões',
            'Decoração',
            'Eletrodomésticos',
            'Eletroportáteis',
            'Esporte e Lazer',
            'Ferramentas',
            'Informática e Games',
            'Instrumentos Musicais',
            'Livros',
            'Moda',
            'Móveis',
            'Música e Shows',
            'Papelaria',
            'Pet Shop',
            'Relógios',
            'Saúde e Cuidados Pessoais',
            'Serviços',
            'Suplementos Alimentares',
            'Tablets, iPads e E-Reader',
            'TV e Vídeo',
            'Passagem',
            'Hospedagem',
            'Outro'
        ];
        
        return [
            'name' => $this->faker->unique()->randomElement($departaments),
        ];
    }
}