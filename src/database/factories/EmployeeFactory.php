<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\Employee;
use App\Models\Sector;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmployeeFactory extends Factory
{
    protected $model = Employee::class;

    public function definition(): array
    {
        $situations = ['CLT', 'PJ', 'PJ_FREELA', 'GESTOR', 'DIRETOR', 'DESLIGADO', 'OUTROS'];
        
        return [
            'name' => $this->faker->name(),
            'cpf' => $this->faker->numerify('###.###.###-##'),
            'key' => $this->faker->unique()->uuid(),
            'fone' => $this->faker->numerify('(##) #####-####'),
            'email' => $this->faker->unique()->safeEmail(),
            'company_id' => Company::inRandomOrder()->first()->id ?? Company::factory()->create()->id,
            'sector_id' => Sector::inRandomOrder()->first()->id ?? Sector::factory()->create()->id,
            'date_of_admission' => $this->faker->dateTimeBetween('-5 years', 'now'),
            'birthday_date' => $this->faker->dateTimeBetween('-60 years', '-18 years'),
            'value' => $this->faker->randomFloat(2, 250, 2500),
            'street' => $this->faker->streetName(),
            'number' => $this->faker->buildingNumber(),
            'complement' => $this->faker->optional()->secondaryAddress(),
            'zip_code' => $this->faker->numerify('#####-###'),
            'bairro' => $this->faker->word(),
            'city' => $this->faker->city(),
            'state' => $this->faker->randomElement(['SP', 'RJ', 'MG', 'RS', 'PR', 'SC', 'BA']),
            'situation' => $this->faker->randomElement($situations),
        ];
    }
}