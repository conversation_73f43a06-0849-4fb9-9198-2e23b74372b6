<?php

namespace Database\Factories;

use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyFactory extends Factory
{
    protected $model = Company::class;

    public function definition(): array
    {
        $companies = [
            'HKM - Essentia Palhoça',
            'SMART - Essentia Florianópolis',
            'ATRIUM - Pedra Branca',
            'INP - INDÚSTRIA - São José',
            'E.V. - São José',
            'ESSENTIA EXPERIENCE - São Paulo',
            'PJ',
            'Não definido',
            'Rancho Queimado',
        ];
        
        return [
            'company_name' => $this->faker->randomElement($companies),
            'cnpj' => $this->faker->numerify('##.###.###/####-##'),
        ];
    }
}