<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            '6 Meses',
            '1 Ano',
            '2 Anos',
            '3 Anos',
            '4 Anos',
            '5 Anos',
            '6 Anos',
            '7 Anos',
            '8 Anos',
            '9 Anos',
            '10 Anos',
            '11 Anos',
            '12 Anos',
            '13 Anos',
            '14 Anos',
            '15 Anos',
            '16 Anos',
            '17 Anos',
            '18 Anos',
            '19 Anos',
        ];

        foreach ($categories as $category) {
            DB::table('categories')->insert([
                'category_name' => $category,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
