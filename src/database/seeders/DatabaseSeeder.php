<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Departament;
use App\Models\Employee;
use App\Models\ItemOrder;
use App\Models\Order;
use App\Models\Sector;
use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Chamar o CategorySeeder
        $this->call(CategorySeeder::class);
        
        // Criar usuário de teste
        // User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        //     'password' => bcrypt('password'),
        // ]);
        
        // Criar algumas empresas
        Company::factory(3)->create();
        
        // Criar alguns setores
        Sector::factory(5)->create();
        
        // Criar todos os departamentos
        $departaments = [
            'Ar e Ventilação',
            'Áudio',
            'Automotivo',
            'Beleza & Perfumaria',
            'Brinque<PERSON>',
            'Cama, Mesa e Banho',
            'Câmeras e Drones',
            'Casa e Construção',
            'Celulares e Smartphones',
            'Colchões',
            'Decoração',
            'Eletrodomésticos',
            'Eletroportáteis',
            'Esporte e Lazer',
            'Ferramentas',
            'Informática e Games',
            'Instrumentos Musicais',
            'Livros',
            'Moda',
            'Móveis',
            'Música e Shows',
            'Papelaria',
            'Pet Shop',
            'Relógios',
            'Saúde e Cuidados Pessoais',
            'Serviços',
            'Suplementos Alimentares',
            'Tablets, iPads e E-Reader',
            'TV e Vídeo',
            'Passagem',
            'Hospedagem',
            'Outro'
        ];
        
        foreach ($departaments as $departament) {
            Departament::create(['name' => $departament]);
        }
        
        // Criar 10 funcionários
        Employee::factory(10)->create()->each(function ($employee) {
            // Criar uma ordem para cada funcionário
            $order = Order::create([
                'employee_id' => $employee->id,
                'status' => 'pending',
                'order_date' => now(),
                'number_gcom' => 'GCOM-' . rand(1000, 9999),
                'status_gcom' => 'Em processamento',
                'ordered_value' => '0.00',
                'approved_value' => '0.00',
            ]);
            
            // Criar 1-3 itens para cada ordem
            $itemCount = 3;
            $departaments = Departament::all();
            
            for ($i = 0; $i < $itemCount; $i++) {
                ItemOrder::create([
                    'order_id' => $order->id,
                    'employee_id' => $employee->id,
                    'session_id' => $departaments->random()->id,
                    'link' => 'https://example.com/product/' . rand(1000, 9999),
                    'description' => 'Produto ' . fake()->word(),
                    'size' => fake()->randomElement(['P', 'M', 'G', 'GG', 'N/A']),
                    'voltage' => fake()->randomElement(['110V', '220V', 'Bivolt', 'N/A']),
                    'model_or_brand' => fake()->company(),
                    'status' => 'pending',
                    'value' => fake()->randomFloat(2, 100, 2500)
                ]);
            }
        });
    }
}
