/* GiftHub - Estilos para Produtos */

/* Estilos específicos para Produtos */
.product-row {
    transition: all 0.3s ease;
}

.product-row:hover {
    background: linear-gradient(135deg, var(--gifthub-gray-50), #f0f0f0) !important;
    transform: translateX(4px);
}

.product-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.product-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid var(--gifthub-gray-200);
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-image:hover {
    transform: scale(1.1);
    border-color: var(--gifthub-primary);
    box-shadow: 0 4px 15px rgba(177, 144, 78, 0.3);
}

.product-image-placeholder {
    width: 60px;
    height: 60px;
    background: var(--gifthub-gray-100);
    border: 2px dashed var(--gifthub-gray-300);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gifthub-gray-400);
    font-size: 1.5rem;
}

.product-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.product-name {
    font-weight: 600;
    color: var(--gifthub-dark);
    font-size: 15px;
    line-height: 1.3;
}

.product-id {
    font-size: 12px;
    color: var(--gifthub-gray-500);
    font-weight: 500;
}

.category-badge {
    background: linear-gradient(135deg, #f8f4e6, #f0e6d2);
    color: var(--gifthub-primary);
    padding: 0.4rem 0.8rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 13px;
    border: 1px solid var(--gifthub-primary);
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

/* Estados Vazios */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--gifthub-gray-500);
    background: var(--gifthub-light);
    border-radius: 12px;
    border: 2px dashed var(--gifthub-gray-200);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
    color: var(--gifthub-primary);
}

.empty-state h4 {
    color: var(--gifthub-dark);
    margin-bottom: 1rem;
    font-weight: 600;
}

.empty-state p {
    color: var(--gifthub-gray-500);
    font-size: 1.1rem;
}

/* Navegação */
.navigation-section {
    margin: 3rem 0 2rem;
    background: var(--gifthub-light);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(20, 20, 20, 0.08);
    border: 1px solid var(--gifthub-gray-200);
}

.navigation-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

.navigation-left {
    justify-self: start;
}

.navigation-center {
    justify-self: center;
}

.navigation-right {
    justify-self: end;
}

.nav-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.navigation-right .nav-info {
    align-items: flex-end;
}

.nav-label {
    font-size: 0.8rem;
    color: var(--gifthub-gray-500);
    font-weight: 500;
}

.nav-name {
    font-size: 0.9rem;
    color: var(--gifthub-dark);
    font-weight: 600;
}

/* Responsividade para Produtos */
@media (max-width: 768px) {
    .navigation-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }
    
    .navigation-left, .navigation-center, .navigation-right {
        justify-self: center;
    }
    
    .nav-info {
        align-items: center !important;
    }
    
    .product-image {
        width: 50px;
        height: 50px;
    }
    
    .product-image-placeholder {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .product-name {
        font-size: 14px;
    }
    
    .product-id {
        font-size: 11px;
    }
    
    .category-badge {
        padding: 0.3rem 0.6rem;
        font-size: 12px;
    }
}
