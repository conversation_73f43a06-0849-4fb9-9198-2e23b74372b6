/* GiftHub - Estilos para <PERSON><PERSON> */

/* Indicador de Progresso */
.progress-container {
    background: var(--gifthub-light);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(20, 20, 20, 0.08);
    border: 1px solid var(--gifthub-gray-200);
    margin-bottom: 2rem;
}

.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gifthub-gray-200);
    color: var(--gifthub-gray-500);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    border: 3px solid var(--gifthub-gray-300);
}

.step-indicator.active .step-circle {
    background: var(--gifthub-primary);
    color: var(--gifthub-light);
    border-color: var(--gifthub-primary);
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(177, 144, 78, 0.3);
}

.step-indicator.completed .step-circle {
    background: var(--gifthub-success);
    color: var(--gifthub-light);
    border-color: var(--gifthub-success);
}

.step-label {
    margin-top: 0.75rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--gifthub-gray-500);
    text-align: center;
    transition: all 0.3s ease;
}

.step-indicator.active .step-label {
    color: var(--gifthub-primary);
    font-weight: 700;
}

.step-indicator.completed .step-label {
    color: var(--gifthub-success);
    font-weight: 600;
}

.progress-line {
    flex: 1;
    height: 4px;
    background: var(--gifthub-gray-200);
    margin: 0 1rem;
    border-radius: 2px;
    position: relative;
    top: -30px;
    z-index: 1;
    transition: all 0.3s ease;
}

.progress-line.completed {
    background: var(--gifthub-success);
}

/* Cards de Formulário */
.card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(20, 20, 20, 0.08);
    border: 1px solid var(--gifthub-gray-200);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(20, 20, 20, 0.12);
    border-color: var(--gifthub-primary);
}

.card-header {
    background: linear-gradient(135deg, var(--gifthub-dark), #1a1a1a);
    color: var(--gifthub-light);
    border-bottom: none;
    font-weight: 600;
    padding: 1.25rem;
    border-radius: 16px 16px 0 0 !important;
}

.card-header.bg-primary {
    background: linear-gradient(135deg, var(--gifthub-primary), var(--gifthub-secondary)) !important;
}

.card-header.bg-success {
    background: linear-gradient(135deg, var(--gifthub-success), #218838) !important;
}

.card-header.bg-info {
    background: linear-gradient(135deg, var(--gifthub-info), #138496) !important;
}

.card-header.bg-warning {
    background: linear-gradient(135deg, var(--gifthub-warning), #e0a800) !important;
    color: var(--gifthub-dark) !important;
}

.card-body {
    padding: 2rem;
}

/* Categorias de Produtos */
.category-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid var(--gifthub-gray-200);
}

.category-card:hover {
    border-color: var(--gifthub-primary);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(177, 144, 78, 0.15);
}

.category-card .card-header {
    background: linear-gradient(135deg, var(--gifthub-gray-100), var(--gifthub-gray-200));
    color: var(--gifthub-dark);
    cursor: pointer;
}

.category-card:hover .card-header {
    background: linear-gradient(135deg, var(--gifthub-primary), var(--gifthub-secondary));
    color: var(--gifthub-light);
}

/* Produtos */
.products-container {
    max-height: 400px;
    overflow-y: auto;
}

.product-row {
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-row:hover {
    background: linear-gradient(135deg, var(--gifthub-gray-50), #f0f0f0);
    transform: translateX(4px);
}

.product-image-cell {
    padding: 0.75rem;
}

.product-img {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.product-row:hover .product-img {
    transform: scale(1.05);
}

.no-image {
    width: 60px;
    height: 60px;
    background: var(--gifthub-gray-100);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gifthub-gray-400);
    font-size: 1.5rem;
}

.product-name {
    font-weight: 600;
    color: var(--gifthub-dark);
    font-size: 14px;
}

/* Modal de Produto */
.no-image-large {
    padding: 3rem;
    color: var(--gifthub-gray-400);
}

/* Tela de Sucesso */
.success-icon {
    font-size: 5rem;
    color: var(--gifthub-success);
    animation: successPulse 2s infinite;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Campos de Formulário Específicos */
.form-label {
    font-weight: 600;
    color: var(--gifthub-dark);
    margin-bottom: 0.5rem;
    font-size: 14px;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--gifthub-primary);
    box-shadow: 0 0 0 3px rgba(177, 144, 78, 0.1);
}

/* Spinner de Carregamento */
#cep-loading {
    background: var(--gifthub-gray-100);
    border: 2px solid var(--gifthub-gray-200);
    border-left: none;
}

/* Badges */
.badge.bg-danger {
    background: var(--gifthub-danger) !important;
}

.badge.bg-warning {
    background: var(--gifthub-warning) !important;
    color: var(--gifthub-dark) !important;
}

/* Responsividade */
@media (max-width: 768px) {
    .progress-container {
        padding: 1.5rem 1rem;
    }
    
    .step-circle {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .step-label {
        font-size: 0.8rem;
    }
    
    .progress-line {
        margin: 0 0.5rem;
        top: -25px;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .products-container {
        max-height: 300px;
    }
}

@media (max-width: 576px) {
    .step-indicator {
        margin-bottom: 1rem;
    }
    
    .progress-line {
        display: none;
    }
    
    .d-flex.justify-content-between.align-items-center {
        flex-direction: column;
        gap: 1rem;
    }
}
