/* GiftHub - Estilos para Colaboradores */



/* Header Section */
.header-section {
    background: var(--gifthub-light);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(20, 20, 20, 0.08);
    border: 1px solid var(--gifthub-gray-200);
}

.breadcrumb-link {
    color: var(--gifthub-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.breadcrumb-link:hover {
    color: var(--gifthub-secondary);
    transform: translateX(2px);
}

.employee-header-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.employee-avatar-large {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--gifthub-dark), #1a1a1a);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gifthub-light);
    font-size: 2rem;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(20, 20, 20, 0.2);
}

.employee-name-large {
    color: var(--gifthub-primary);
    font-weight: 700;
}

/* Status Cards */
.status-card {
    background: var(--gifthub-light);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.status-card.status-success {
    border-color: var(--gifthub-success);
    background: linear-gradient(135deg, #f8fff9, #e8f5e8);
}

.status-card.status-warning {
    border-color: var(--gifthub-primary);
    background: linear-gradient(135deg, #fffcf5, #f8f4e6);
}

.status-card.status-danger {
    border-color: var(--gifthub-danger);
    background: linear-gradient(135deg, #fff8f8, #f8d7da);
}

.status-icon {
    font-size: 2rem;
}

.status-card.status-success .status-icon { color: var(--gifthub-success); }
.status-card.status-warning .status-icon { color: var(--gifthub-primary); }
.status-card.status-danger .status-icon { color: var(--gifthub-danger); }

.status-info {
    display: flex;
    flex-direction: column;
}

.status-label {
    font-size: 0.9rem;
    color: var(--gifthub-gray-500);
    font-weight: 500;
}

.status-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--gifthub-dark);
}

/* Info Cards */
.info-card {
    background: var(--gifthub-light);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(20, 20, 20, 0.08);
    border: 1px solid var(--gifthub-gray-200);
    transition: all 0.3s ease;
    height: 100%;
}

.info-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(20, 20, 20, 0.15);
    border-color: var(--gifthub-primary);
}

.info-card-header {
    background: linear-gradient(135deg, var(--gifthub-dark), #1a1a1a);
    color: var(--gifthub-light);
    padding: 1.5rem;
    border-radius: 16px 16px 0 0;
}

.info-card-body {
    padding: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-label {
    font-size: 0.9rem;
    color: var(--gifthub-gray-500);
    font-weight: 600;
    display: flex;
    align-items: center;
}

.info-value {
    font-size: 1rem;
    color: var(--gifthub-dark);
    font-weight: 500;
}

/* Badges Específicos */
.key-badge-large {
    background: linear-gradient(135deg, #f8f4e6, #f0e6d2);
    color: var(--gifthub-primary);
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-weight: 700;
    border: 2px solid var(--gifthub-primary);
    display: inline-block;
}

.situation-badge-large {
    background: var(--gifthub-gray-100);
    color: var(--gifthub-dark);
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    display: inline-block;
}

.time-badge {
    background: var(--gifthub-gray-500);
    color: var(--gifthub-light);
    padding: 0.3rem 0.8rem;
    border-radius: 8px;
    font-weight: 500;
    margin-top: 0.5rem;
    display: inline-block;
}

.value-badge-large {
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-weight: 700;
    font-size: 1rem;
    display: inline-block;
}

.value-badge-large.primary {
    background: linear-gradient(135deg, #f8f4e6, #f0e6d2);
    color: var(--gifthub-primary);
    border: 2px solid var(--gifthub-primary);
}

.value-badge-large.success {
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    color: var(--gifthub-success);
    border: 2px solid var(--gifthub-success);
}

.value-badge-large.info {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1976d2;
    border: 2px solid #1976d2;
}

.status-badge-large {
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge-large.status-success {
    background: var(--gifthub-success);
    color: var(--gifthub-light);
}

.status-badge-large.status-warning {
    background: var(--gifthub-primary);
    color: var(--gifthub-light);
}

.status-badge-large.status-danger {
    background: var(--gifthub-danger);
    color: var(--gifthub-light);
}

.empty-state-small {
    text-align: center;
    padding: 2rem;
    color: var(--gifthub-gray-500);
}

.empty-state-small i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Seções */
.wishes-section, .history-section {
    margin: 3rem 0;
}

.wishes-header, .history-header {
    text-align: center;
    margin-bottom: 2rem;
}

.section-title {
    color: var(--gifthub-dark);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.section-title i {
    color: var(--gifthub-primary);
}

.section-subtitle {
    color: var(--gifthub-gray-500);
    font-size: 1.1rem;
}

/* Tabelas de Desejos e Histórico */
.wish-row, .history-row {
    transition: all 0.3s ease;
}

.wish-row:hover, .history-row:hover {
    background: linear-gradient(135deg, var(--gifthub-gray-50), #f0f0f0) !important;
    transform: translateX(4px);
}

.wish-number-badge, .history-number-badge {
    background: var(--gifthub-primary);
    color: var(--gifthub-light);
    padding: 0.3rem 0.6rem;
    border-radius: 50%;
    font-weight: 700;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 30px;
    height: 30px;
}

.history-number-badge {
    background: var(--gifthub-gray-400);
}

.description-cell {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    line-height: 1.4;
}

.size-badge, .brand-badge, .voltage-badge {
    background: var(--gifthub-gray-100);
    color: var(--gifthub-dark);
    padding: 0.2rem 0.5rem;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    display: inline-block;
}

.size-badge-history {
    background: var(--gifthub-gray-200);
    color: var(--gifthub-gray-600);
    padding: 0.2rem 0.5rem;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.status-badge {
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    white-space: nowrap;
}

.status-badge.status-success {
    background: var(--gifthub-success);
    color: var(--gifthub-light);
}

.status-badge.status-warning {
    background: var(--gifthub-primary);
    color: var(--gifthub-light);
}

.status-badge.status-danger {
    background: var(--gifthub-danger);
    color: var(--gifthub-light);
}



.price-badge {
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    color: var(--gifthub-success);
    padding: 0.3rem 0.6rem;
    border-radius: 8px;
    font-weight: 700;
    font-size: 12px;
    border: 1px solid var(--gifthub-success);
    display: inline-block;
}

.price-badge-history {
    background: var(--gifthub-gray-100);
    color: var(--gifthub-gray-600);
    padding: 0.3rem 0.6rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 12px;
    display: inline-block;
}

.product-link {
    color: var(--gifthub-primary);
    text-decoration: none;
    font-weight: 600;
    font-size: 13px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.product-link:hover {
    color: var(--gifthub-secondary);
    transform: translateX(2px);
}

.product-link-history {
    color: var(--gifthub-gray-500);
    text-decoration: none;
    font-weight: 500;
    font-size: 13px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.product-link-history:hover {
    color: var(--gifthub-primary);
    transform: translateX(2px);
}

/* Estilos específicos para listagem de funcionários */

/* Cards de Estatísticas */
.stats-card {
    border-radius: 16px;
    padding: 1.5rem;
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.bg-gradient-primary { background: linear-gradient(135deg, #141414, #1a1a1a) !important; }
.bg-gradient-success { background: linear-gradient(135deg, #28a745, #34ce57) !important; }
.bg-gradient-warning { background: linear-gradient(135deg, #b1904e, #c9a55a) !important; }
.bg-gradient-danger { background: linear-gradient(135deg, #dc3545, #e74c3c) !important; }

.stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stats-content {
    display: flex;
    flex-direction: column;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: white;
}

.stats-number-success {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stats-number-warning {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stats-number-danger {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stats-label {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
    color: white;
}

/* Card de Filtros */
.filter-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.filter-card .card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    border-radius: 16px 16px 0 0 !important;
}

/* Tabela Moderna para Listagem */
.table-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    z-index: 1;
}

/* Estilos específicos para o header da tabela */
.table-modern {
    margin: 0;
    border: none;
    position: relative;
    z-index: 1;
}

.table-modern thead {
    background: linear-gradient(135deg, var(--gifthub-dark), #1a1a1a);
    color: var(--gifthub-light);
    position: relative;
    z-index: 2;
}

.table-modern thead th {
    border: none;
    padding: 1rem;
    font-weight: 600;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    vertical-align: middle;
    color: var(--gifthub-light) !important;
    background: linear-gradient(135deg, var(--gifthub-dark), #1a1a1a) !important;
}

/* Garantir que todos os elementos do header sejam brancos */
.table-modern thead th,
.table-modern thead th a,
.table-modern thead th i,
.table-modern thead th .sort-link,
.table-modern thead th .sort-link i {
    color: var(--gifthub-light) !important;
}

/* Específico para colunas sem links */
.table-modern thead th:not(.sortable-header) {
    color: var(--gifthub-light) !important;
}

.sortable-header {
    position: relative;
}

.sort-link {
    color: var(--gifthub-light) !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.sort-link:hover {
    color: #f8f9fa !important;
    transform: translateY(-1px);
}

.sort-indicator {
    opacity: 0.7;
    transition: all 0.3s ease;
}

.sort-indicator.inactive {
    opacity: 0.4;
}

.sort-link:hover .sort-indicator {
    opacity: 1;
}

.table-modern tbody tr {
    border: none;
    transition: all 0.3s ease;
    background: white;
    position: relative;
    z-index: 1;
}

.table-modern tbody td {
    padding: 1rem;
    border-top: 1px solid var(--gifthub-gray-100);
    vertical-align: middle;
    font-size: 14px;
}

.table-row {
    background: white;
}

.table-row:hover {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Garantir que as bordas das linhas funcionem */
.row-success {
    border-left: 4px solid var(--gifthub-success);
}

.row-warning {
    border-left: 4px solid var(--gifthub-primary);
}

.row-danger {
    border-left: 4px solid var(--gifthub-danger);
}

.row-default {
    border-left: 4px solid var(--gifthub-gray-200);
}

/* Componentes da Tabela */
.employee-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.employee-avatar-small {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #141414, #1a1a1a);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.employee-details-small {
    min-width: 0;
}

.employee-name-small {
    font-weight: 600;
    color: #b1904e;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Badges específicos para listagem */
.status-success { background: #d1ecf1; color: #0c5460; }
.status-warning { background: #f8f4e6; color: #6d5a2e; }
.status-danger { background: #f8d7da; color: #721c24; }
.status-secondary { background: #e2e3e5; color: #383d41; }

.key-badge {
    background: #f8f4e6;
    color: #b1904e;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    border: 1px solid #b1904e;
}

.value-badge {
    background: #d1ecf1;
    color: #0c5460;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    border: 1px solid #28a745;
}

.situation-badge {
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    background: #f1f3f4;
    color: #495057;
}

.date-info {
    display: flex;
    flex-direction: column;
    gap: 0.1rem;
}

.company-info, .sector-info {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.9rem;
}

.approved-wishes-table {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    max-width: 200px;
}

.wish-item-table {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.8rem;
    padding: 0.2rem 0.4rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #28a745;
}

/* Spinner no botão */
.filter-loading {
    display: none;
}

/* Paginação */
.pagination-container {
    margin-top: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination-info {
    font-size: 0.9rem;
}

/* Navegação */
.navigation-section {
    margin: 3rem 0 2rem;
    background: var(--gifthub-light);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(20, 20, 20, 0.08);
    border: 1px solid var(--gifthub-gray-200);
}

.navigation-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

.navigation-left {
    justify-self: start;
}

.navigation-center {
    justify-self: center;
}

.navigation-right {
    justify-self: end;
}

.nav-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.navigation-right .nav-info {
    align-items: flex-end;
}

.nav-label {
    font-size: 0.8rem;
    color: var(--gifthub-gray-500);
    font-weight: 500;
}

.nav-name {
    font-size: 0.9rem;
    color: var(--gifthub-dark);
    font-weight: 600;
}

/* Responsividade para Employees */
@media (max-width: 768px) {
    .navigation-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }

    .navigation-left, .navigation-center, .navigation-right {
        justify-self: center;
    }

    .nav-info {
        align-items: center !important;
    }
}

/* Estilos para checkbox de situação */
.form-check-input:checked {
    background-color: var(--gifthub-danger);
    border-color: var(--gifthub-danger);
}

.form-check-label.text-danger {
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-check-label.text-danger:hover {
    color: var(--gifthub-danger) !important;
    transform: translateX(2px);
}

/* Estilos para botões de ação */
.action-buttons {
    display: flex;
    gap: 0.3rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons .btn {
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Estilos específicos para o botão warning */
.btn-warning {
    background: linear-gradient(135deg, var(--gifthub-primary), var(--gifthub-accent));
    border-color: var(--gifthub-primary);
    color: var(--gifthub-light);
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--gifthub-secondary), var(--gifthub-primary));
    border-color: var(--gifthub-secondary);
    color: var(--gifthub-light);
}

/* Melhorias no modal de alerta */
.alert-warning {
    border-left: 4px solid var(--gifthub-primary);
    background: linear-gradient(135deg, #fffcf5, #f8f4e6);
    border-color: var(--gifthub-primary);
}

.alert-warning .fas {
    color: var(--gifthub-primary);
}
