/* GiftHub - Tema Global */
/* Importar fonte moderna */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Variáveis CSS - Cores GiftHub */
:root {
    --gifthub-primary: #b1904e;
    --gifthub-secondary: #9a7d42;
    --gifthub-accent: #c9a55a;
    --gifthub-dark: #141414;
    --gifthub-darker: #0a0a0a;
    --gifthub-light: #ffffff;
    --gifthub-success: #28a745;
    --gifthub-warning: #ffc107;
    --gifthub-danger: #dc3545;
    --gifthub-info: #17a2b8;
    --gifthub-gray-50: #f8f9fa;
    --gifthub-gray-100: #e9ecef;
    --gifthub-gray-200: #dee2e6;
    --gifthub-gray-300: #ced4da;
    --gifthub-gray-400: #6c757d;
    --gifthub-gray-500: #495057;
    --gifthub-gray-600: #343a40;
    --gifthub-gray-700: #212529;
    --gifthub-gray-800: #1a1a1a;
    --gifthub-gray-900: #141414;
}

/* Reset e configurações globais */
html, body {
    position: relative;
    overflow-x: hidden;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--gifthub-gray-50);
    color: var(--gifthub-dark);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* Tipografia */
h1, h2, h3, h4, h5, h6 {
    color: var(--gifthub-dark);
    font-weight: 600;
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    color: var(--gifthub-dark);
    font-weight: 700;
}

.text-primary { color: var(--gifthub-primary) !important; }
.text-secondary { color: var(--gifthub-secondary) !important; }
.text-success { color: var(--gifthub-success) !important; }
.text-warning { color: var(--gifthub-warning) !important; }
.text-danger { color: var(--gifthub-danger) !important; }
.text-info { color: var(--gifthub-info) !important; }

/* Links */
a {
    color: var(--gifthub-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--gifthub-secondary);
    text-decoration: none;
}

/* Navbar - Estilo GiftHub */
.navbar {
    background: var(--gifthub-darker) !important;
    border-bottom: 1px solid var(--gifthub-gray-200);
    box-shadow: 0 2px 10px rgba(20, 20, 20, 0.08);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    color: var(--gifthub-primary) !important;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: var(--gifthub-secondary) !important;
    transform: scale(1.05);
}

.navbar-brand img {
    max-height: 40px;
    transition: all 0.3s ease;
}

.navbar-brand:hover img {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    color: var(--gifthub-light) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--gifthub-primary) !important;
    background: var(--gifthub-gray-50);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    color: var(--gifthub-primary) !important;
    background: linear-gradient(135deg, var(--gifthub-gray-50), #f0f0f0);
    font-weight: 600;
}

.navbar-toggler {
    border: 2px solid var(--gifthub-primary);
    border-radius: 8px;
    padding: 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 3px rgba(177, 144, 78, 0.1);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='%23b1904e' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(20, 20, 20, 0.15);
    border: 1px solid var(--gifthub-gray-200);
    margin-top: 0.5rem;
}

.dropdown-item {
    color: var(--gifthub-dark);
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: var(--gifthub-gray-50);
    color: var(--gifthub-primary);
    transform: translateX(4px);
}

.dropdown-divider {
    border-color: var(--gifthub-gray-200);
}



/* Correção específica para tabelas com header escuro */
.table-modern thead th {
    background: linear-gradient(135deg, var(--gifthub-dark), #1a1a1a) !important;
    color: var(--gifthub-light) !important;
}

.table-modern thead th,
.table-modern thead th *,
.table-modern thead th a,
.table-modern thead th i {
    color: var(--gifthub-light) !important;
}

/* Botões - Estilo GiftHub */
.btn {
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 14px;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
}

.btn-primary {
    background: var(--gifthub-primary);
    color: var(--gifthub-light);
    border-color: var(--gifthub-primary);
}

.btn-primary:hover {
    background: var(--gifthub-secondary);
    border-color: var(--gifthub-secondary);
    color: var(--gifthub-light);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(177, 144, 78, 0.3);
}

.btn-success {
    background: var(--gifthub-success);
    color: var(--gifthub-light);
    border-color: var(--gifthub-success);
}

.btn-success:hover {
    background: #218838;
    border-color: #218838;
    color: var(--gifthub-light);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-danger {
    background: var(--gifthub-danger);
    color: var(--gifthub-light);
    border-color: var(--gifthub-danger);
}

.btn-danger:hover {
    background: #c82333;
    border-color: #c82333;
    color: var(--gifthub-light);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-secondary {
    background: var(--gifthub-gray-100);
    color: var(--gifthub-dark);
    border-color: var(--gifthub-gray-200);
}

.btn-secondary:hover {
    background: var(--gifthub-gray-200);
    color: var(--gifthub-dark);
    border-color: var(--gifthub-primary);
    transform: translateY(-2px);
}

.btn-outline-primary {
    background: transparent;
    color: var(--gifthub-primary);
    border-color: var(--gifthub-primary);
}

.btn-outline-primary:hover {
    background: var(--gifthub-primary);
    color: var(--gifthub-light);
    border-color: var(--gifthub-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(177, 144, 78, 0.3);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--gifthub-gray-500);
    border-color: var(--gifthub-gray-300);
}

.btn-outline-secondary:hover {
    background: var(--gifthub-gray-100);
    color: var(--gifthub-dark);
    border-color: var(--gifthub-gray-400);
    transform: translateY(-2px);
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(20, 20, 20, 0.08);
    transition: all 0.3s ease;
    background: var(--gifthub-light);
    border: 1px solid var(--gifthub-gray-200);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(20, 20, 20, 0.15);
    border-color: var(--gifthub-primary);
}

.card-header {
    background: linear-gradient(135deg, var(--gifthub-dark), var(--gifthub-gray-800));
    color: var(--gifthub-light);
    border-bottom: none;
    font-weight: 600;
    padding: 1.25rem;
    border-radius: 12px 12px 0 0 !important;
}

.card-body {
    padding: 1.5rem;
}

/* Formulários */
.form-label {
    font-weight: 600;
    color: var(--gifthub-dark);
    margin-bottom: 0.5rem;
    font-size: 14px;
}

.form-control, .form-select {
    border: 2px solid var(--gifthub-gray-200);
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 14px;
    transition: all 0.3s ease;
    background: var(--gifthub-light);
    color: var(--gifthub-dark);
}

.form-control:focus, .form-select:focus {
    border-color: var(--gifthub-primary);
    box-shadow: 0 0 0 3px rgba(177, 144, 78, 0.1);
    background: var(--gifthub-light);
    outline: none;
}

.form-control::placeholder {
    color: var(--gifthub-gray-400);
}

/* Input Groups */
.input-group-text {
    background: var(--gifthub-gray-100);
    border: 2px solid var(--gifthub-gray-200);
    border-right: none;
    color: var(--gifthub-gray-600);
    font-weight: 600;
}

.input-group .form-control {
    border-left: none;
}

.input-group .form-control:focus {
    border-left: none;
}

/* Tabelas Modernas */
.table-modern {
    margin: 0;
    border: none;
    background: var(--gifthub-light);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(20, 20, 20, 0.08);
}

.table-modern thead {
    background: linear-gradient(135deg, var(--gifthub-dark), var(--gifthub-gray-800));
    color: var(--gifthub-light);
}

.table-modern thead th {
    border: none;
    padding: 1rem;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.5px;
    vertical-align: middle;
}

.table-modern tbody tr {
    border: none;
    transition: all 0.3s ease;
    background: var(--gifthub-light);
}

.table-modern tbody tr:hover {
    background: linear-gradient(135deg, var(--gifthub-gray-50), #f0f0f0);
    transform: translateX(4px);
}

.table-modern tbody td {
    padding: 1rem;
    border-top: 1px solid var(--gifthub-gray-100);
    vertical-align: middle;
    font-size: 14px;
}

/* Badges */
.badge {
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 12px;
    letter-spacing: 0.5px;
}

.badge.bg-primary { background: var(--gifthub-primary) !important; }
.badge.bg-success { background: var(--gifthub-success) !important; }
.badge.bg-warning { background: var(--gifthub-warning) !important; }
.badge.bg-danger { background: var(--gifthub-danger) !important; }
.badge.bg-info { background: var(--gifthub-info) !important; }

/* Alertas */
.alert {
    border: none;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-primary { border-left-color: var(--gifthub-primary); }
.alert-success { border-left-color: var(--gifthub-success); }
.alert-warning { border-left-color: var(--gifthub-warning); }
.alert-danger { border-left-color: var(--gifthub-danger); }
.alert-info { border-left-color: var(--gifthub-info); }

/* Modais */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(20, 20, 20, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, var(--gifthub-dark), var(--gifthub-gray-800));
    color: var(--gifthub-light);
    border-bottom: none;
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid var(--gifthub-gray-200);
}

/* Breadcrumbs */
.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: var(--gifthub-primary);
    transition: all 0.3s ease;
}

.breadcrumb-item a:hover {
    color: var(--gifthub-secondary);
    transform: translateX(2px);
}

.breadcrumb-item.active {
    color: var(--gifthub-gray-500);
}

/* Paginação */
.pagination .page-link {
    color: var(--gifthub-primary);
    border-color: var(--gifthub-gray-200);
    background: var(--gifthub-light);
}

.pagination .page-link:hover {
    color: var(--gifthub-light);
    background: var(--gifthub-primary);
    border-color: var(--gifthub-primary);
}

.pagination .page-item.active .page-link {
    background: var(--gifthub-primary);
    border-color: var(--gifthub-primary);
    color: var(--gifthub-light);
}

/* Utilitários */
.text-muted {
    color: var(--gifthub-gray-500) !important;
}

.bg-light {
    background: var(--gifthub-gray-50) !important;
}

.bg-dark {
    background: var(--gifthub-dark) !important;
}

.border-primary {
    border-color: var(--gifthub-primary) !important;
}

/* Responsividade */
@media (max-width: 768px) {
    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 13px;
    }

    .card-body {
        padding: 1rem;
    }

    .table-modern thead th,
    .table-modern tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 13px;
    }
}
