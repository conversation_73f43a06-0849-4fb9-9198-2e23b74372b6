<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\MeuDesejoController;
use App\Http\Controllers\ProductController;

Route::get('/', function () {
    if (Auth::check()) {
        return redirect()->route('employees.index'); // Redireciona para home se estiver logado
    }
    return view('auth.login'); // Mostra a tela de login se não estiver logado
});

Auth::routes();

Route::middleware(['auth'])->group(function () {
    Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
    Route::get('/employees', [EmployeeController::class, 'index'])->name('employees.index');
    Route::get('/employees/show', [EmployeeController::class, 'show'])->name('employees.show');
    Route::patch('/items/{id}/approve', [EmployeeController::class, 'approveItem'])->name('items.approve');
    Route::patch('/items/{id}/reprove', [EmployeeController::class, 'reproveItem'])->name('items.reprove');

    // Products CRUD routes
    Route::resource('products', ProductController::class);
});

Route::get('/meu-desejo', [MeuDesejoController::class, 'index'])->name('meu-desejo');
Route::post('/verificar-pedido', [MeuDesejoController::class, 'verificarPedido']);
Route::post('/salvar-etapa1', [MeuDesejoController::class, 'salvarEtapa1']);
Route::post('/salvar-itens', [MeuDesejoController::class, 'salvarItens']);
Route::get('/produtos-por-categoria', [MeuDesejoController::class, 'getProductsByCategory']);