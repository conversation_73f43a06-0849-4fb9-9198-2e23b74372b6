<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\Employee;
use App\Models\Company;
use App\Models\Sector;
use App\Models\Order;
use App\Models\ItemOrder;
use App\Models\Departament;
use App\Models\Category;
use App\Models\Product;

class MeuDesejoController extends Controller
{
    public function index()
    {
        $categories = Category::all();
        return view('meu-desejo.index', compact('categories'));
    }

    public function verificarPedido(Request $request)
    {
        $cpf = $request->cpf;

        // Remover caracteres não numéricos para garantir consistência
        $cpfLimpo = preg_replace('/[^0-9]/', '', $cpf);

        // Validar formato do CPF
        if (strlen($cpfLimpo) !== 11) {
            return response()->json(['error' => 'CPF inválido. Deve conter 11 dígitos.'], 400);
        }

        // Buscar funcionário pelo CPF (formatado ou não)
        $employee = Employee::where('cpf', $cpf)
            ->orWhere('cpf', $cpfLimpo)
            ->orWhere('cpf', preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $cpfLimpo))
            ->first();

        if (!$employee) {
            return response()->json(['error' => 'Funcionário não encontrado.'], 404);
        }

        // Verificar se já existe um pedido para este funcionário
        $order = Order::where('employee_id', $employee->id)->first();

        if ($order) {
            // Se o pedido está pendente ou aprovado, não permite novo pedido
            if (in_array($order->status, ['pending', 'approved'])) {
                if (ItemOrder::where('order_id', $order->id)->count() > 0) {
                    $statusMessage = $order->status === 'pending' ? 'pendente' : 'aprovado';
                    return response()->json([
                        'error' => "Você já possui um pedido {$statusMessage}. Não é possível fazer um novo pedido no momento.",
                        'order_status' => $order->status,
                        'order_date' => $order->order_date
                    ]);
                }
            }

        }

        return response()->json([
            'employee' => $employee,
            'companies' => Company::all(),
            'sectors' => Sector::all(),
            'existing_order' => $order ? [
                'id' => $order->id,
                'status' => $order->status,
                'order_date' => $order->order_date
            ] : null
        ]);
    }

    public function salvarEtapa1(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'company_id' => 'required|exists:companies,id',
            'sector_id' => 'required|exists:sectors,id',
            'date_of_admission' => 'required|date',
            'birthday_date' => 'required|date',
            'phone' => 'nullable|string',
            'fone' => 'nullable|string',
            'email' => 'nullable|email',
            'zip_code' => 'nullable|string',
            'street' => 'nullable|string',
            'number' => 'nullable|string',
            'complement' => 'nullable|string',
            'bairro' => 'nullable|string',
            'city' => 'nullable|string',
            'state' => 'nullable|string',
        ]);

        $employee = Employee::findOrFail($request->employee_id);

        // Preparar dados para atualização
        $updateData = $request->only([
            'fone', 'email', 'company_id', 'sector_id',
            'date_of_admission', 'birthday_date', 'zip_code', 'street',
            'number', 'complement', 'bairro', 'city', 'state'
        ]);

        // Se phone foi enviado, usar como fone (compatibilidade)
        if ($request->has('phone') && !empty($request->phone)) {
            $updateData['fone'] = $request->phone;
        }

        $employee->update($updateData);

        // Debug: Log dos dados atualizados (remover em produção)
        Log::info('Dados atualizados do funcionário:', [
            'employee_id' => $employee->id,
            'update_data' => $updateData,
            'employee_after_update' => $employee->fresh()->toArray()
        ]);

        $order = Order::where('employee_id', $employee->id)->first();

        if (!$order) {
            $order = Order::create([
                'employee_id' => $employee->id,
                'status' => 'pending',
                'order_date' => now(),
            ]);
        } else {
            // Se o pedido foi rejeitado, atualizar status para pendente
            if ($order->status === 'rejected') {
                $order->update([
                    'status' => 'pending',
                    'order_date' => now(),
                ]);
            }
        }

        return response()->json([
            'order_id' => $order->id,
            'departaments' => Departament::all(),
        ]);
    }

    public function salvarItens(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
            'items' => 'required|array|min:3|max:3',
            'items.1.link' => 'required|string',
            'items.2.link' => 'required|string',
            'items.3.link' => 'required|string',
            'items.*.size' => 'nullable|string',
            'items.*.voltage' => 'nullable|string',
            'items.*.model_or_brand' => 'nullable|string',
            'items.*.description' => 'nullable|string',
            'items.*.value' => 'nullable|string',
        ], [
            'items.min' => 'Você deve preencher todos os 3 desejos.',
            'items.max' => 'Você pode preencher no máximo 3 desejos.',
            'items.1.link.required' => 'O link do Desejo 1 é obrigatório.',
            'items.2.link.required' => 'O link do Desejo 2 é obrigatório.',
            'items.3.link.required' => 'O link do Desejo 3 é obrigatório.',
        ]);

        $order = Order::where('id', $request->order_id)->first();

        // Verificar se o pedido pode ser modificado
        if (in_array($order->status, ['approved'])) {
            return response()->json(['error' => 'Não é possível modificar um pedido aprovado.'], 400);
        }

        foreach ($request->items as $item) {
            // Verificar se o link não está vazio
            if (empty(trim($item['link']))) {
                continue;
            }

            // Processar o valor monetário
            $value = 0.00;
            if (!empty($item['value'])) {
                // Remover caracteres não numéricos exceto vírgula e ponto
                $cleanValue = preg_replace('/[^0-9,.]/', '', $item['value']);
                // Substituir vírgula por ponto para conversão
                $cleanValue = str_replace(',', '.', $cleanValue);
                // Converter para float
                $value = (float) $cleanValue;
            }

            // Debug: Log do valor antes de salvar (remover em produção)
            Log::info('Valor do item antes de salvar:', [
                'order_id' => $request->order_id,
                'item_link' => $item['link'],
                'item_value_original' => $item['value'] ?? 'null',
                'item_value_processed' => $value
            ]);

            ItemOrder::create([
                'order_id' => $request->order_id,
                'employee_id' => $order->employee_id,
                'session_id' => $item['session_id'] ?? null,
                'link' => trim($item['link']),
                'size' => $item['size'] ?? null,
                'voltage' => $item['voltage'] ?? null,
                'model_or_brand' => $item['model_or_brand'] ?? null,
                'description' => $item['description'] ?? null,
                'value' => $value,
                'status' => 'pending',
            ]);
        }

        // Atualizar status do pedido para pendente
        $order->update([
            'status' => 'pending',
            'order_date' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Seus desejos foram enviados com sucesso!',
            'order_id' => $order->id
        ]);
    }

    public function getProductsByCategory(Request $request)
    {
        $categoryId = $request->category_id;

        $products = Product::where('category_id', $categoryId)
            ->orderBy('product_name')
            ->get();

        return response()->json([
            'products' => $products
        ]);
    }
}
