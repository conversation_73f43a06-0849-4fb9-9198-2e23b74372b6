<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Employee;
use App\Models\Order;
use App\Models\ItemOrder;

class EmployeeController extends Controller
{
    public function index(Request $request)
    {
        // Capturar os parâmetros de filtro e a quantidade de registros por página
        $search = $request->input('search');
        $statusFilter = $request->input('status');
        $sortColumn = $request->input('sort', 'id'); // Padrão: 'id'
        $sortDirection = $request->input('direction', 'asc'); // Padrão: 'asc'
        $perPage = $request->input('per_page', 10); // Padrão: 5 registros por página
        $showAll = $request->input('show_all', false); // Novo parâmetro para mostrar todos

        // Query com filtros dinâmicos
        $query = Employee::with(['orders.items', 'company', 'sector'])
            ->when($search, function ($query) use ($search) {
                return $query->where('name', 'like', "%{$search}%")
                    ->orWhere('key', 'like', "%{$search}%")
                    ->orWhere('cpf', 'like', "%{$search}%")
                    ->orWhereHas('items', function ($subQuery) use ($search) {
                        $subQuery->where('description', 'like', "%{$search}%");
                    });
            })
            ->when($statusFilter, function ($query) use ($statusFilter) {
                return $query->whereHas('orders', function ($subQuery) use ($statusFilter) {
                    $subQuery->where('status', $statusFilter);
                });
            })
            ->orderBy($sortColumn, $sortDirection);

        // Decidir se pagina ou retorna todos
        if ($showAll) {
            $allEmployees = $query->get();
            $employees = new \Illuminate\Pagination\LengthAwarePaginator(
                $allEmployees,
                $allEmployees->count(),
                $allEmployees->count() ?: 1,
                1,
                ['path' => $request->url(), 'query' => $request->query()]
            );
        } else {
            $employees = $query->paginate($perPage)->withQueryString();
        }

        return view('employees.index', compact('employees', 'search', 'sortColumn', 'sortDirection', 'statusFilter', 'perPage', 'showAll'));
    }

    public function show(Request $request)
    {
        $id = $request->input('id');
        $employee = Employee::with(['orders.items', 'company', 'sector'])->findOrFail($id);
        $departaments = \App\Models\Departament::all();
        $nextEmployee = Employee::where('id', '>', $id)->orderBy('id')->first();
        $beforeEmployee = Employee::where('id', '<', $id)->orderBy('id', 'desc')->first();

        return view('employees.show', compact('employee', 'nextEmployee', 'beforeEmployee', 'departaments'));
    }

    public function approveItem(Request $request, $id)
    {
        // Desmarcar todos os itens do funcionário
        $employee = Employee::findOrFail($request->employee_id);

        foreach ($employee->items as $item) {
            $item->status = 'reproved';
            $item->save();
        }

        // Aprovar o item selecionado e atualizar seus dados
        $item = ItemOrder::findOrFail($id);
        $item->status = 'approved';

        // Atualizar os campos editados no modal
        if ($request->has('description')) {
            $item->description = $request->description;
        }
        if ($request->has('size')) {
            $item->size = $request->size;
        }
        if ($request->has('model_or_brand')) {
            $item->model_or_brand = $request->model_or_brand;
        }
        if ($request->has('voltage')) {
            $item->voltage = $request->voltage;
        }
        if ($request->has('session_id')) {
            $item->session_id = $request->session_id;
        }
        if ($request->has('value')) {
            $item->value = $request->value;
        }

        $item->save();

        // Aprovar a ordem relacionada ao item
        $order = Order::findOrFail($item->order_id);
        $order->approved_value = $item->value;
        $order->status = 'approved';
        $order->save();

        return redirect()->back()->with('success', 'Item aprovado com sucesso!');
    }

public function reproveItem(Request $request, $id)
{

    $employee = Employee::findOrFail($request->employee_id);



    // Aprovar o item selecionado
    $item = ItemOrder::findOrFail($id);
    $item->status = 'reproved';
    $item->save();

    // Verificar se todos os itens estão com o status de reproved
    $allReproved = $employee->items->every(function ($item) {
        return $item->status === 'reproved';
    });

    // Aprovar a ordem relacionada aos itens
    $order = Order::findOrFail($item->order_id);
    $order->status = $allReproved ? 'reproved' : 'pending';
    $order->save();

    return redirect()->back()->with('success', 'Item reproved with success!');

}
}
