<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Employee;
use App\Models\Order;
use App\Models\ItemOrder;
use GuzzleHttp\Exception\GuzzleException;

class EmployeeController extends Controller
{
    public function index(Request $request)
    {
        // Capturar os parâmetros de filtro e a quantidade de registros por página
        $search = $request->input('search');
        $statusFilter = $request->input('status');
        $sortColumn = $request->input('sort', 'id'); // Padrão: 'id'
        $sortDirection = $request->input('direction', 'asc'); // Padrão: 'asc'
        $perPage = $request->input('per_page', 10); // Padrão: 5 registros por página
        $showAll = $request->input('show_all', false); // Novo parâmetro para mostrar todos

        // Query com filtros dinâmicos
        $query = Employee::with(['orders.items', 'company', 'sector'])
            ->when($search, function ($query) use ($search) {
                return $query->where('name', 'like', "%{$search}%")
                    ->orWhere('key', 'like', "%{$search}%")
                    ->orWhere('cpf', 'like', "%{$search}%")
                    ->orWhereHas('items', function ($subQuery) use ($search) {
                        $subQuery->where('description', 'like', "%{$search}%");
                    });
            })
            ->when($statusFilter, function ($query) use ($statusFilter) {
                return $query->whereHas('orders', function ($subQuery) use ($statusFilter) {
                    $subQuery->where('status', $statusFilter);
                });
            })
            ->orderBy($sortColumn, $sortDirection);

        // Decidir se pagina ou retorna todos
        if ($showAll) {
            $allEmployees = $query->get();
            $employees = new \Illuminate\Pagination\LengthAwarePaginator(
                $allEmployees,
                $allEmployees->count(),
                $allEmployees->count() ?: 1,
                1,
                ['path' => $request->url(), 'query' => $request->query()]
            );
        } else {
            $employees = $query->paginate($perPage)->withQueryString();
        }

        return view('employees.index', compact('employees', 'search', 'sortColumn', 'sortDirection', 'statusFilter', 'perPage', 'showAll'));
    }

    public function show(Request $request)
    {
        $id = $request->input('id');
        $employee = Employee::with(['orders.items', 'company', 'sector'])->findOrFail($id);
        $departaments = \App\Models\Departament::all();
        $nextEmployee = Employee::where('id', '>', $id)->orderBy('id')->first();
        $beforeEmployee = Employee::where('id', '<', $id)->orderBy('id', 'desc')->first();

        return view('employees.show', compact('employee', 'nextEmployee', 'beforeEmployee', 'departaments'));
    }

    public function approveItem(Request $request, $id)
    {
        // Desmarcar todos os itens do funcionário
        $employee = Employee::findOrFail($request->employee_id);

        foreach ($employee->items as $item) {
            $item->status = 'reproved';
            $item->save();
        }

        // Aprovar o item selecionado e atualizar seus dados
        $item = ItemOrder::findOrFail($id);
        $item->status = 'approved';

        // Atualizar os campos editados no modal
        if ($request->has('description')) {
            $item->description = $request->description;
        }
        if ($request->has('size')) {
            $item->size = $request->size;
        }
        if ($request->has('model_or_brand')) {
            $item->model_or_brand = $request->model_or_brand;
        }
        if ($request->has('voltage')) {
            $item->voltage = $request->voltage;
        }
        if ($request->has('session_id')) {
            $item->session_id = $request->session_id;
        }
        if ($request->has('value')) {
            $item->value = $request->value;
        }

        $item->save();

        // Integração com o GCOM
        $this->integraGcom($employee->id);

        // Aprovar a ordem relacionada ao item
        $order = Order::findOrFail($item->order_id);
        $order->approved_value = $item->value;
        $order->status = 'approved';
        $order->save();

        return redirect()->back()->with('success', 'Item aprovado com sucesso!');
    }

    public function reproveItem(Request $request, $id)
    {

        $employee = Employee::findOrFail($request->employee_id);

        // Aprovar o item selecionado
        $item = ItemOrder::findOrFail($id);
        $item->status = 'reproved';
        $item->save();

        // Verificar se todos os itens estão com o status de reproved
        $allReproved = $employee->items->every(function ($item) {
            return $item->status === 'reproved';
        });

        // Aprovar a ordem relacionada aos itens
        $order = Order::findOrFail($item->order_id);
        $order->status = $allReproved ? 'reproved' : 'pending';
        $order->save();

        return redirect()->back()->with('success', 'Item reproved with success!');

    }

    public function approveAndReproveOthers(Request $request, $id)
    {
        // Buscar o funcionário
        $employee = Employee::findOrFail($request->employee_id);

        // Reprovar todos os itens do funcionário primeiro
        foreach ($employee->items as $item) {
            $item->status = 'reproved';
            $item->save();
        }

        // Aprovar apenas o item selecionado e atualizar seus dados
        $item = ItemOrder::findOrFail($id);
        $item->status = 'approved';

        // Atualizar os campos editados no modal
        if ($request->has('description')) {
            $item->description = $request->description;
        }
        if ($request->has('size')) {
            $item->size = $request->size;
        }
        if ($request->has('model_or_brand')) {
            $item->model_or_brand = $request->model_or_brand;
        }
        if ($request->has('voltage')) {
            $item->voltage = $request->voltage;
        }
        if ($request->has('session_id')) {
            $item->session_id = $request->session_id;
        }
        if ($request->has('value')) {
            $item->value = $request->value;
        }

        $item->save();

        // Aprovar a ordem relacionada ao item
        $order = Order::findOrFail($item->order_id);
        $order->approved_value = $item->value;
        $order->status = 'approved';
        $order->save();

        return redirect()->back()->with('success', 'Item aprovado e demais reprovados com sucesso!');
    }

    public function updateSituation(Request $request, $id)
    {
        $employee = Employee::findOrFail($id);
        $order = Order::where('employee_id', $employee->id)->first();

        // Verificar se o checkbox foi marcado
        if ($request->has('desligado') && $request->desligado == '1') {
            $employee->situation = 'DESLIGADO';
            $order->status = 'reproved';
            $order->save();
            // Reprovar todos os itens do funcionário primeiro
            foreach ($employee->items as $item) {
                $item->status = 'reproved';
                $item->save();
            }
        } else {
            // Se desmarcado, voltar para uma situação padrão (pode ser ajustado conforme necessário)
            $employee->situation = 'CLT'; // ou outra situação padrão
        }

        $employee->save();

        $message = $employee->situation === 'DESLIGADO'
            ? 'Colaborador marcado como DESLIGADO com sucesso!'
            : 'Situação do colaborador atualizada com sucesso!';

        return redirect()->back()->with('success', $message);
    }

    public function integraGcom($employee_id){
        $employee = Employee::findOrFail($employee_id);
        $order = Order::where('employee_id', $employee->id)->first();
        $order_items = ItemOrder::where('order_id', $order->id)->where('status', 'approved')->get();
        
        $json = [
            'employee' => $employee->toArray(),
            'item' => $order_items->toArray(),
        ];

        json_encode($json);
        dd($json);
        return redirect()->back()->with('success', 'Integração com o GCOM realizada com sucesso!');
    }
}
