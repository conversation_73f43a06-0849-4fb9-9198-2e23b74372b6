<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Employee extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'cpf', 'key', 'fone', 'phone', 'email', 'company_id', 'sector_id', 'date_of_admission', 'birthday_date',
        'value', 'street', 'zip_code', 'city', 'state', 'complement', 'bairro', 'neighborhood', 'number', 'situation'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function sector()
    {
        return $this->belongsTo(Sector::class, 'sector_id');
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'employee_id');
    }

    public function items()
    {
        return $this->hasMany(ItemOrder::class, 'employee_id');
    }
    // Desativa o atualizar a data de criação e updated_at
    public $timestamps = false;
}
