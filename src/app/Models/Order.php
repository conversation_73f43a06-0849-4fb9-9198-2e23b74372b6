<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    protected $fillable = ['employee_id', 'status_gcom', 'order_date', 'number_gcom', 'status'];

    // Relacionamento: Uma empresa tem vários funcionários
    public function employees()
    {
        return $this->belongsTo(Employee::class);
    }

    public function items()
    {
        return $this->hasMany(ItemOrder::class);
    }
}
