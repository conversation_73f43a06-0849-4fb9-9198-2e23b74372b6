<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ItemOrder extends Model
{
    use HasFactory;

    protected $fillable = ['order_id', 'employee_id', 'session_id', 'link', 'size','description','voltage', 'model_or_brand', 'status', 'value'];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function departament()
    {
        return $this->belongsTo(Departament::class);
    }
}
