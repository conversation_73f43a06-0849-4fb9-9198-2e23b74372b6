<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class EnsureStorageLink extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:ensure-link';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ensure storage link exists and create product directories';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $publicPath = public_path('storage');
        $storagePath = storage_path('app/public');

        // Create storage/app/public if it doesn't exist
        if (!File::exists($storagePath)) {
            File::makeDirectory($storagePath, 0755, true);
            $this->info('Created storage directory: ' . $storagePath);
        }

        // Create storage/app/public/products if it doesn't exist
        $productsPath = $storagePath . '/products';
        if (!File::exists($productsPath)) {
            File::makeDirectory($productsPath, 0755, true);
            $this->info('Created products directory: ' . $productsPath);
        }

        // Create symbolic link if it doesn't exist
        if (!File::exists($publicPath)) {
            $this->call('storage:link');
        } else {
            $this->info('Storage link already exists.');
        }

        $this->info('Storage setup completed successfully.');
    }
}
